//
//  FriendEmptyPlaceholderView.swift
//  Shuxiaoqi
//
//  Created by yongsheng ye on 2025/7/26.
//

import UIKit
import SnapKit

/// 朋友页面空状态占位视图
class FriendEmptyPlaceholderView: UIView {
    
    // MARK: - UI Components
    
    /// 主图标容器
    private lazy var iconContainerView: UIView = {
        let view = UIView()
        view.backgroundColor = .clear
        return view
    }()
    
    /// 视频播放器图标
    private lazy var videoPlayerIconView: UIImageView = {
        let imageView = UIImageView()
        imageView.contentMode = .scaleAspectFit
        imageView.backgroundColor = UIColor(hex: "#666666")
        imageView.layer.cornerRadius = 8
        return imageView
    }()
    
    /// 播放按钮图标
    private lazy var playButtonIconView: UIImageView = {
        let imageView = UIImageView()
        imageView.image = UIImage(systemName: "play.fill")
        imageView.tintColor = .white
        imageView.contentMode = .scaleAspectFit
        return imageView
    }()
    
    /// 搜索图标
    private lazy var searchIconView: UIImageView = {
        let imageView = UIImageView()
        imageView.image = UIImage(systemName: "magnifyingglass")
        imageView.tintColor = UIColor(hex: "#999999")
        imageView.contentMode = .scaleAspectFit
        return imageView
    }()
    
    /// 装饰线条
    private lazy var decorativeLines: [UIView] = {
        var lines: [UIView] = []
        for _ in 0..<3 {
            let line = UIView()
            line.backgroundColor = UIColor(hex: "#CCCCCC")
            line.layer.cornerRadius = 1
            lines.append(line)
        }
        return lines
    }()
    
    /// 选项容器
    private lazy var optionsContainerView: UIView = {
        let view = UIView()
        view.backgroundColor = .clear
        return view
    }()
    
    /// 扫一扫选项
    private lazy var scanOptionView: FriendOptionView = {
        let view = FriendOptionView(
            iconName: "qrcode.viewfinder",
            title: "扫一扫",
            subtitle: "扫描树小柒码加好友"
        )
        view.backgroundColor = UIColor(hex: "#1C1C1C")
        view.layer.cornerRadius = 12
        return view
    }()
    
    /// 添加微信好友选项
    private lazy var wechatOptionView: FriendOptionView = {
        let view = FriendOptionView(
            iconName: "message.fill",
            title: "添加微信好友",
            subtitle: "分享我的口令到微信添加好友"
        )
        view.backgroundColor = UIColor(hex: "#1C1C1C")
        view.layer.cornerRadius = 12
        return view
    }()
    
    // MARK: - Callbacks
    var onScanTapped: (() -> Void)?
    var onWechatTapped: (() -> Void)?
    
    // MARK: - Initialization
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
        setupGestures()
    }
    
    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupUI()
        setupGestures()
    }
    
    // MARK: - UI Setup
    
    private func setupUI() {
        backgroundColor = .black
        
        // 添加主图标容器
        addSubview(iconContainerView)
        iconContainerView.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.centerY.equalToSuperview().offset(-120)
            make.width.equalTo(200)
            make.height.equalTo(120)
        }
        
        // 添加视频播放器图标
        iconContainerView.addSubview(videoPlayerIconView)
        videoPlayerIconView.snp.makeConstraints { make in
            make.left.top.equalToSuperview()
            make.width.equalTo(120)
            make.height.equalTo(80)
        }
        
        // 添加播放按钮
        videoPlayerIconView.addSubview(playButtonIconView)
        playButtonIconView.snp.makeConstraints { make in
            make.center.equalToSuperview()
            make.width.height.equalTo(24)
        }
        
        // 添加搜索图标
        iconContainerView.addSubview(searchIconView)
        searchIconView.snp.makeConstraints { make in
            make.right.bottom.equalToSuperview()
            make.width.height.equalTo(32)
        }
        
        // 添加装饰线条
        for (index, line) in decorativeLines.enumerated() {
            iconContainerView.addSubview(line)
            let width: CGFloat = [60, 40, 50][index]
            line.snp.makeConstraints { make in
                make.left.equalTo(videoPlayerIconView.snp.right).offset(20)
                make.top.equalTo(videoPlayerIconView).offset(CGFloat(index * 15) + 15)
                make.width.equalTo(width)
                make.height.equalTo(2)
            }
        }
        
        // 添加选项容器
        addSubview(optionsContainerView)
        optionsContainerView.snp.makeConstraints { make in
            make.top.equalTo(iconContainerView.snp.bottom).offset(60)
            make.left.equalToSuperview().offset(20)
            make.right.equalToSuperview().offset(-20)
            make.height.equalTo(160)
        }
        
        // 添加扫一扫选项
        optionsContainerView.addSubview(scanOptionView)
        scanOptionView.snp.makeConstraints { make in
            make.top.left.right.equalToSuperview()
            make.height.equalTo(70)
        }
        
        // 添加微信好友选项
        optionsContainerView.addSubview(wechatOptionView)
        wechatOptionView.snp.makeConstraints { make in
            make.top.equalTo(scanOptionView.snp.bottom).offset(20)
            make.left.right.equalToSuperview()
            make.height.equalTo(70)
        }
    }
    
    private func setupGestures() {
        let scanTapGesture = UITapGestureRecognizer(target: self, action: #selector(scanOptionTapped))
        scanOptionView.addGestureRecognizer(scanTapGesture)
        
        let wechatTapGesture = UITapGestureRecognizer(target: self, action: #selector(wechatOptionTapped))
        wechatOptionView.addGestureRecognizer(wechatTapGesture)
    }
    
    // MARK: - Actions
    
    @objc private func scanOptionTapped() {
        onScanTapped?()
    }
    
    @objc private func wechatOptionTapped() {
        onWechatTapped?()
    }
}

// MARK: - FriendOptionView

/// 朋友页面选项视图
class FriendOptionView: UIView {
    
    private lazy var iconImageView: UIImageView = {
        let imageView = UIImageView()
        imageView.contentMode = .scaleAspectFit
        imageView.tintColor = UIColor(hex: "#FF8F1F")
        return imageView
    }()
    
    private lazy var titleLabel: UILabel = {
        let label = UILabel()
        label.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        label.textColor = .white
        return label
    }()
    
    private lazy var subtitleLabel: UILabel = {
        let label = UILabel()
        label.font = UIFont.systemFont(ofSize: 14)
        label.textColor = UIColor(hex: "#999999")
        return label
    }()
    
    private lazy var arrowImageView: UIImageView = {
        let imageView = UIImageView()
        imageView.image = UIImage(systemName: "chevron.right")
        imageView.tintColor = UIColor(hex: "#666666")
        imageView.contentMode = .scaleAspectFit
        return imageView
    }()
    
    init(iconName: String, title: String, subtitle: String) {
        super.init(frame: .zero)
        
        iconImageView.image = UIImage(systemName: iconName)
        titleLabel.text = title
        subtitleLabel.text = subtitle
        
        setupUI()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    private func setupUI() {
        addSubview(iconImageView)
        addSubview(titleLabel)
        addSubview(subtitleLabel)
        addSubview(arrowImageView)
        
        iconImageView.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(16)
            make.centerY.equalToSuperview()
            make.width.height.equalTo(24)
        }
        
        titleLabel.snp.makeConstraints { make in
            make.left.equalTo(iconImageView.snp.right).offset(12)
            make.top.equalToSuperview().offset(16)
        }
        
        subtitleLabel.snp.makeConstraints { make in
            make.left.equalTo(titleLabel)
            make.top.equalTo(titleLabel.snp.bottom).offset(4)
            make.right.lessThanOrEqualTo(arrowImageView.snp.left).offset(-8)
        }
        
        arrowImageView.snp.makeConstraints { make in
            make.right.equalToSuperview().offset(-16)
            make.centerY.equalToSuperview()
            make.width.height.equalTo(16)
        }
    }
}
