//
//  FriendEmptyPlaceholderView.swift
//  Shu<PERSON><PERSON>qi
//
//  Created by AI Assistant on 2025/07/26.
//

import UIKit
import SnapKit

/// 朋友页面空状态占位视图
class FriendEmptyPlaceholderView: UIView {
    
    // MARK: - 回调闭包
    var onScanTapped: (() -> Void)?
    var onWechatTapped: (() -> Void)?
    
    // MARK: - UI 组件
    
    /// 主要图标 - friend_empty 128*128pt
    private lazy var mainIconView: UIImageView = {
        let imageView = UIImageView()
        imageView.contentMode = .scaleAspectFit
        imageView.image = UIImage(named: "friend_empty")
        return imageView
    }()
    
    /// 选项堆栈视图
    private lazy var optionsStackView: UIStackView = {
        let stackView = UIStackView()
        stackView.axis = .vertical
        stackView.spacing = 0
        stackView.distribution = .fill
        return stackView
    }()
    
    // MARK: - 初始化
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
    }
    
    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupUI()
    }
    
    // MARK: - UI 设置
    
    private func setupUI() {
        backgroundColor = .white
        
        // 添加主要图标
        addSubview(mainIconView)
        
        // 添加选项堆栈视图
        addSubview(optionsStackView)
        
        // 添加选项行
        let scanOption = createOptionView(
            icon: "sharing_sys",
            title: "扫一扫",
            subtitle: "扫描树小柒码加好友",
            action: #selector(scanButtonTapped)
        )
        
        let wechatOption = createOptionView(
            icon: "sharing_wx",
            title: "添加微信好友",
            subtitle: "分享我的口令到微信添加好友",
            action: #selector(wechatButtonTapped)
        )
        
        // 添加到堆栈视图
        optionsStackView.addArrangedSubview(scanOption)
        optionsStackView.addArrangedSubview(addSeparator())
        optionsStackView.addArrangedSubview(wechatOption)
        
        setupConstraints()
    }
    
    private func setupConstraints() {
        // 主要图标约束 - 128*128pt
        mainIconView.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.centerY.equalToSuperview().offset(-120)
            make.size.equalTo(128)
        }
        
        // 选项堆栈视图约束
        optionsStackView.snp.makeConstraints { make in
            make.leading.trailing.equalToSuperview().inset(20)
            make.top.equalTo(mainIconView.snp.bottom).offset(80)
        }
    }

    // MARK: - 创建选项视图 (复制自 UserSharingViewController)

    private func createOptionView(icon: String, title: String, subtitle: String, action: Selector) -> UIView {
        let container = UIView()
        container.backgroundColor = .white

        // 图标
        let iconView = UIImageView()
        if let systemImage = UIImage(systemName: icon) {
            iconView.image = systemImage
            iconView.tintColor = UIColor.systemBlue
        } else {
            iconView.image = UIImage(named: icon)
        }
        iconView.contentMode = .scaleAspectFit

        // 标题
        let titleLabel = UILabel()
        titleLabel.text = title
        titleLabel.textColor = .black
        titleLabel.font = UIFont.systemFont(ofSize: 16)

        // 副标题
        let subtitleLabel = UILabel()
        subtitleLabel.text = subtitle
        subtitleLabel.textColor = .darkGray
        subtitleLabel.font = UIFont.systemFont(ofSize: 12)

        // 箭头
        let arrowView = UIImageView(image: UIImage(named: "sharing_right_arrow"))
        arrowView.contentMode = .scaleAspectFit
        arrowView.tintColor = .lightGray

        // 添加到容器
        container.addSubview(iconView)
        container.addSubview(titleLabel)
        container.addSubview(subtitleLabel)
        container.addSubview(arrowView)

        // 设置约束
        iconView.snp.makeConstraints { make in
            make.leading.equalToSuperview().offset(20)
            make.centerY.equalToSuperview()
            make.size.equalTo(30)
        }

        titleLabel.snp.makeConstraints { make in
            make.leading.equalTo(iconView.snp.trailing).offset(15)
            make.bottom.equalTo(container.snp.centerY).offset(-2)
        }

        subtitleLabel.snp.makeConstraints { make in
            make.leading.equalTo(titleLabel)
            make.top.equalTo(container.snp.centerY).offset(2)
        }

        arrowView.snp.makeConstraints { make in
            make.trailing.equalToSuperview().offset(-20)
            make.centerY.equalToSuperview()
            make.size.equalTo(20)
        }

        // 设置容器高度
        container.snp.makeConstraints { make in
            make.height.equalTo(60)
        }

        // 添加点击手势
        let tapGesture = UITapGestureRecognizer(target: self, action: action)
        container.addGestureRecognizer(tapGesture)
        container.isUserInteractionEnabled = true

        return container
    }

    // MARK: - 添加分隔线 (复制自 UserSharingViewController)

    private func addSeparator() -> UIView {
        let separator = UIView()
        separator.backgroundColor = UIColor.lightGray.withAlphaComponent(0.3)
        separator.snp.makeConstraints { make in
            make.height.equalTo(0.5)
        }
        return separator
    }

    // MARK: - 手势响应

    @objc private func scanButtonTapped() {
        onScanTapped?()
    }

    @objc private func wechatButtonTapped() {
        onWechatTapped?()
    }
}
