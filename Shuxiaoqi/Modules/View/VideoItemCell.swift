//
//  VideoItemCell.swift
//  Shu<PERSON><PERSON>qi
//
//  Created by yong<PERSON>ng ye on 2025/4/2.
//
import UIKit
import Kingfisher

class VideoItemCell: UICollectionViewCell {
    private let containerView = UIView()
    private let titleLabel = UILabel()
    private let imageView = UIImageView()
    
    // 添加新的UI组件
    private let avatarContainer = UIView() // 头像容器,用于添加渐变边框
    private let avatarImageView = UIImageView() // 头像图片
    private let authorLabel = UILabel() // 作者名称
    private let progressView = UIView() // 进度条背景
    private let progressBar = UIView() // 进度条前景
    
    // 添加直播标签视图
    private let liveTagView: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor.hexString("#EEEEEE").withAlphaComponent(0.5)
        view.layer.cornerRadius = 10
        view.isHidden = true
        // 阴影可选
        return view
    }()
    private let liveDotView: UIView = {
        let dot = UIView()
        dot.backgroundColor = UIColor(hex: "#FF904C")
        dot.layer.cornerRadius = 3
        return dot
    }()
    private let liveLabel: UILabel = {
        let label = UILabel()
        label.text = "直播中"
        label.textColor = .white
        label.font = .systemFont(ofSize: 12, weight: .medium)
        return label
    }()

    // 添加播放类型图标视图
    private lazy var playTypeIconView: UIImageView = {
        let imageView = UIImageView()
        imageView.image = UIImage(named: "play_type_cell")
        imageView.isHidden = true
        return imageView
    }()
    
    // 用于处理Cell复用时的异步加载
    private var currentImageName: String?
    private var currentAvatarName: String?
    
    private var progressBarWidthConstraint: NSLayoutConstraint?
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        
        // 调整: 允许透明背景，解决圆角处出现白色底色问题
        self.isOpaque = false
        self.backgroundColor = .clear // 透明背景，使用父视图背景色
        contentView.isOpaque = false
        contentView.backgroundColor = .clear // 透明背景
        
        // 设置容器视图
        // 背景设为透明，由 imageView 填充内容
        containerView.backgroundColor = .clear
        containerView.layer.cornerRadius = 15
        containerView.clipsToBounds = true
        
        // 设置封面图
        imageView.translatesAutoresizingMaskIntoConstraints = false
        imageView.layer.cornerRadius = 15
        imageView.clipsToBounds = true
        imageView.contentMode = .scaleAspectFill
        imageView.isOpaque = true
        imageView.backgroundColor = .darkGray // 图片加载前的占位背景色
        
        // 设置标题标签
        titleLabel.textColor = .white
        titleLabel.font = .systemFont(ofSize: 14, weight: .medium)
        titleLabel.numberOfLines = 2
        
        // 设置头像容器
        avatarContainer.backgroundColor = .clear
        avatarContainer.layer.cornerRadius = 20 // 半径为头像宽度的一半
        // 添加渐变边框
        let gradientLayer = CAGradientLayer()
        gradientLayer.frame = CGRect(x: 0, y: 0, width: 40, height: 40)
        gradientLayer.colors = [
//            UIColor(red: 1, green: 0.89, blue: 0.71, alpha: 1).cgColor,
//            UIColor(red: 1, green: 0.55, blue: 0.21, alpha: 1).cgColor
            UIColor(hex: "#FF8D36").cgColor,
            UIColor(hex: "#FF5858").cgColor
        ]
        gradientLayer.startPoint = CGPoint(x: 0, y: 0)
        gradientLayer.endPoint = CGPoint(x: 1, y: 1)
        gradientLayer.cornerRadius = 20
        avatarContainer.layer.addSublayer(gradientLayer)
        
        // 设置头像图片
        avatarImageView.backgroundColor = .lightGray // 图片加载前的占位背景色
        avatarImageView.layer.cornerRadius = 18 // 留出2pt边框
        avatarImageView.clipsToBounds = true
        avatarImageView.contentMode = .scaleAspectFill
        avatarImageView.isOpaque = true
        
        // 设置作者名称
        authorLabel.textColor = .white
        authorLabel.font = .boldSystemFont(ofSize: 16)
        
        // 设置进度条
        progressView.backgroundColor = UIColor.white.withAlphaComponent(0.2)
        progressBar.backgroundColor = UIColor.white.withAlphaComponent(0.6)
        
        // 添加到视图层级
        contentView.addSubview(containerView)
        containerView.addSubview(imageView)
        containerView.addSubview(titleLabel)
        containerView.addSubview(avatarContainer)
        avatarContainer.addSubview(avatarImageView)
        containerView.addSubview(authorLabel)
        containerView.addSubview(progressView)
        progressView.addSubview(progressBar)
        containerView.addSubview(liveTagView)
        liveTagView.addSubview(liveDotView)
        liveTagView.addSubview(liveLabel)
        containerView.addSubview(playTypeIconView)
        
        // 设置约束
        containerView.translatesAutoresizingMaskIntoConstraints = false
        titleLabel.translatesAutoresizingMaskIntoConstraints = false
        avatarContainer.translatesAutoresizingMaskIntoConstraints = false
        avatarImageView.translatesAutoresizingMaskIntoConstraints = false
        authorLabel.translatesAutoresizingMaskIntoConstraints = false
        progressView.translatesAutoresizingMaskIntoConstraints = false
        progressBar.translatesAutoresizingMaskIntoConstraints = false
        liveTagView.translatesAutoresizingMaskIntoConstraints = false
        liveDotView.translatesAutoresizingMaskIntoConstraints = false
        liveLabel.translatesAutoresizingMaskIntoConstraints = false
        playTypeIconView.translatesAutoresizingMaskIntoConstraints = false
        
        NSLayoutConstraint.activate([
            // 容器视图约束
            containerView.topAnchor.constraint(equalTo: contentView.topAnchor),
            containerView.leadingAnchor.constraint(equalTo: contentView.leadingAnchor),
            containerView.trailingAnchor.constraint(equalTo: contentView.trailingAnchor),
            containerView.bottomAnchor.constraint(equalTo: contentView.bottomAnchor),
            
            // 封面图约束
            imageView.topAnchor.constraint(equalTo: containerView.topAnchor),
            imageView.leadingAnchor.constraint(equalTo: containerView.leadingAnchor),
            imageView.trailingAnchor.constraint(equalTo: containerView.trailingAnchor),
            imageView.bottomAnchor.constraint(equalTo: containerView.bottomAnchor),
            
            // 头像容器约束
            avatarContainer.leadingAnchor.constraint(equalTo: containerView.leadingAnchor, constant: 12),
            avatarContainer.bottomAnchor.constraint(equalTo: containerView.bottomAnchor, constant: -62),
            avatarContainer.widthAnchor.constraint(equalToConstant: 40),
            avatarContainer.heightAnchor.constraint(equalToConstant: 40),
            
            // 头像图片约束
            avatarImageView.centerXAnchor.constraint(equalTo: avatarContainer.centerXAnchor),
            avatarImageView.centerYAnchor.constraint(equalTo: avatarContainer.centerYAnchor),
            avatarImageView.widthAnchor.constraint(equalToConstant: 36), // 留出2pt边框
            avatarImageView.heightAnchor.constraint(equalToConstant: 36),
            
            // 作者名称约束
            authorLabel.centerYAnchor.constraint(equalTo: avatarContainer.centerYAnchor),
            authorLabel.leadingAnchor.constraint(equalTo: avatarContainer.trailingAnchor, constant: 8),
            authorLabel.trailingAnchor.constraint(equalTo: containerView.trailingAnchor, constant: -12),
            
            // 标题约束
            titleLabel.leadingAnchor.constraint(equalTo: containerView.leadingAnchor, constant: 12),
            titleLabel.topAnchor.constraint(equalTo: avatarContainer.bottomAnchor, constant: 8),
            titleLabel.trailingAnchor.constraint(equalTo: containerView.trailingAnchor, constant: -12),
            titleLabel.heightAnchor.constraint(equalToConstant: 45),
            
            // 进度条约束
            progressView.leadingAnchor.constraint(equalTo: containerView.leadingAnchor),
            progressView.trailingAnchor.constraint(equalTo: containerView.trailingAnchor),
            progressView.bottomAnchor.constraint(equalTo: containerView.bottomAnchor),
            progressView.heightAnchor.constraint(equalToConstant: 4),
            
            // 进度条前景约束
            progressBar.leadingAnchor.constraint(equalTo: progressView.leadingAnchor),
            progressBar.topAnchor.constraint(equalTo: progressView.topAnchor),
            progressBar.bottomAnchor.constraint(equalTo: progressView.bottomAnchor),
            // 宽度约束稍后动态添加
            
            // 直播标签约束
            liveTagView.topAnchor.constraint(equalTo: containerView.topAnchor, constant: 11),
            liveTagView.leadingAnchor.constraint(equalTo: containerView.leadingAnchor, constant: 13),
            liveTagView.widthAnchor.constraint(equalToConstant: 65),
            liveTagView.heightAnchor.constraint(equalToConstant: 20),
            // 圆点约束
            liveDotView.centerYAnchor.constraint(equalTo: liveTagView.centerYAnchor),
            liveDotView.leadingAnchor.constraint(equalTo: liveTagView.leadingAnchor, constant: 8),
            liveDotView.widthAnchor.constraint(equalToConstant: 6),
            liveDotView.heightAnchor.constraint(equalToConstant: 6),
            // 文字约束
            liveLabel.centerYAnchor.constraint(equalTo: liveTagView.centerYAnchor),
            liveLabel.leadingAnchor.constraint(equalTo: liveDotView.trailingAnchor, constant: 6),
            liveLabel.trailingAnchor.constraint(equalTo: liveTagView.trailingAnchor, constant: -8),

            // 播放类型图标约束
            playTypeIconView.topAnchor.constraint(equalTo: containerView.topAnchor, constant: 8),
            playTypeIconView.trailingAnchor.constraint(equalTo: containerView.trailingAnchor, constant: -8),
            playTypeIconView.widthAnchor.constraint(equalToConstant: 20),
            playTypeIconView.heightAnchor.constraint(equalToConstant: 20)
        ])

        // 初始化进度条宽度约束（默认 0）
        progressBarWidthConstraint = progressBar.widthAnchor.constraint(equalTo: progressView.widthAnchor, multiplier: 0)
        progressBarWidthConstraint?.isActive = true
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    override func prepareForReuse() {
        super.prepareForReuse()
        // 重置Cell状态，防止复用时显示旧数据
        imageView.image = nil // 或者设置为统一的占位图
        avatarImageView.image = nil // 或者设置为统一的占位图
        titleLabel.text = nil
        authorLabel.text = nil
        
        // 重置进度条
        updateProgressBar(videoDuration: 0, currentPlayTime: 0)
        
        // 取消与此Cell关联的图像加载任务的标记
        currentImageName = nil
        currentAvatarName = nil
        // 如果使用SDWebImage等库，在这里调用取消加载的方法，例如:
        // imageView.sd_cancelCurrentImageLoad()
        // avatarImageView.sd_cancelCurrentImageLoad()
        liveTagView.isHidden = true
        playTypeIconView.isHidden = true
    }
    
    func configure(with title: String, imageName: String, authorName: String? = nil, avatarUrl: String? = nil, isLive: Bool = false, videoDuration: Float = 0, currentPlayTime: Float = 0, worksType: Int? = nil) {
        titleLabel.text = title
        authorLabel.text = authorName ?? "作者名称" // 使用传入的作者名，如果为nil则使用默认值

        // --- 异步加载主图片 ---
        let placeholderCover = UIImage(named: "placeholder_cover")
        self.currentImageName = imageName
        if imageName.hasPrefix("http") || imageName.hasPrefix("https") {
            // 远程图片，使用 Kingfisher 加载
            if let url = URL(string: imageName) {
                imageView.kf.setImage(with: url, placeholder: placeholderCover)
            } else {
                imageView.image = placeholderCover
            }
        } else {
            // 本地资源
            let image = UIImage(named: imageName) ?? placeholderCover
            imageView.image = image
        }

        // --- 异步加载头像图片 ---
        let placeholderAvatar = UIImage(named: "placeholder_avatar") // 请确保有名为 "placeholder_avatar" 的图片资源
        
        if let avatarUrl = avatarUrl, !avatarUrl.isEmpty {
            // 处理URL，确保是完整路径
            var fullAvatarUrl = avatarUrl
            if !avatarUrl.hasPrefix("http") {
                fullAvatarUrl = "https://test-youshu.gzyoushu.com" + avatarUrl
            }
            
            // 使用Kingfisher加载头像
            if let url = URL(string: fullAvatarUrl) {
                self.currentAvatarName = fullAvatarUrl
                avatarImageView.kf.setImage(with: url, placeholder: placeholderAvatar)
            } else {
                avatarImageView.image = placeholderAvatar
            }
        } else {
            // 使用默认头像
            let avatarImageNameToLoad = "default_avatar"
            self.currentAvatarName = avatarImageNameToLoad
            self.avatarImageView.image = UIImage(named: avatarImageNameToLoad) ?? placeholderAvatar
        }

        // 直播标签显示控制
        liveTagView.isHidden = !isLive

        // 播放类型图标显示控制：只有视频类型(worksType=1)才显示播放图标
        if let worksType = worksType {
            playTypeIconView.isHidden = worksType != 1
        } else {
            // 如果没有类型信息，默认显示播放图标（向后兼容）
            playTypeIconView.isHidden = false
        }

        // 更新进度条
        updateProgressBar(videoDuration: videoDuration, currentPlayTime: currentPlayTime)
    }
    
    // 更新进度条
    func updateProgressBar(videoDuration: Float, currentPlayTime: Float) {
        // 移除之前的约束
        progressBarWidthConstraint?.isActive = false
        
        if videoDuration > 0 {
            // 计算进度比例
            let progress = min(max(currentPlayTime / videoDuration, 0), 1)
            
            // 设置进度条宽度
            progressBarWidthConstraint = progressBar.widthAnchor.constraint(equalTo: progressView.widthAnchor, multiplier: CGFloat(progress))
            progressBarWidthConstraint?.isActive = true
        } else {
            // 如果没有有效的视频时长，设置默认进度为0
            progressBarWidthConstraint = progressBar.widthAnchor.constraint(equalTo: progressView.widthAnchor, multiplier: 0)
            progressBarWidthConstraint?.isActive = true
        }
        
        // 强制更新布局
        layoutIfNeeded()
    }

    // 新增：支持直接以 VideoItem 配置
    func configure(with item: VideoItem) {
        configure(
            with: item.worksTitle ?? "",
            imageName: item.fullCoverImageURL ?? "",
            authorName: item.svUserMainVo?.customerName,
            avatarUrl: item.svUserMainVo?.fullAvatarURL,
            isLive: false, // VideoItem中没有直播状态，默认为false
            videoDuration: Float(item.duration ?? 0),
            currentPlayTime: 0, // 默认播放进度为0
            worksType: item.worksType
        )
    }
}
