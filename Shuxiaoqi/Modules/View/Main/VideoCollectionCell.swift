//
//  VideoCollectionCell.swift
//  Shuxiaoqi
//
//  Created by yongsheng ye on 2025/4/26.
//

import UIKit

// 视频集合单元格类
class VideoCollectionCell: UITableViewCell {
    // 添加 collectionView 属性声明
    private var collectionView: UICollectionView!
    weak var delegate: VideoCollectionCellDelegate?
    private var sectionTitle: String = ""
    
    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        
        // 设置单元格
        selectionStyle = .none
        backgroundColor = .appBackgroundGray
        
        // 创建集合视图布局
        let layout = UICollectionViewFlowLayout()
        layout.scrollDirection = .horizontal
        layout.itemSize = CGSize(width: 230, height: 408)
        layout.minimumLineSpacing = 20
        layout.sectionInset = UIEdgeInsets(top: 10, left: 20, bottom: 10, right: 20)
        
        // 创建集合视图
        collectionView = UICollectionView(frame: .zero, collectionViewLayout: layout)
        collectionView.backgroundColor = .appBackgroundGray
        collectionView.showsHorizontalScrollIndicator = false
        collectionView.delegate = self
        collectionView.dataSource = self
        
        // 注册单元格
        collectionView.register(VideoItemCell.self, forCellWithReuseIdentifier: "VideoItemCell")
        
        // 添加到内容视图
        contentView.addSubview(collectionView)
        
        // 设置约束
        collectionView.translatesAutoresizingMaskIntoConstraints = false
        NSLayoutConstraint.activate([
            collectionView.topAnchor.constraint(equalTo: contentView.topAnchor),
            collectionView.leadingAnchor.constraint(equalTo: contentView.leadingAnchor),
            collectionView.trailingAnchor.constraint(equalTo: contentView.trailingAnchor),
            collectionView.bottomAnchor.constraint(equalTo: contentView.bottomAnchor)
        ])
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    func configure(with title: String) {
        sectionTitle = title
        collectionView.reloadData()
    }
}

// 集合视图代理和数据源方法
extension VideoCollectionCell: UICollectionViewDelegate, UICollectionViewDataSource {
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        return 5 // 每行显示5个视频
    }
    
    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        let cell = collectionView.dequeueReusableCell(withReuseIdentifier: "VideoItemCell", for: indexPath) as! VideoItemCell
        
        // 模拟视频时长，实际应从视频数据中获取
        let videoDuration: Float = 120.0 // 假设视频长度为120秒
        
        // 模拟当前播放时间，实际应从播放器获取
        let currentPlayTime: Float = videoDuration * 0.3 // 假设播放了30%
        
        cell.configure(with: "\(sectionTitle) \(indexPath.item + 1)",
                      imageName: "t00\(indexPath.item + 1)",
                      authorName: "示例作者",
                      avatarUrl: nil,
                      isLive: false,
                      videoDuration: videoDuration,
                      currentPlayTime: currentPlayTime,
                      worksType: 1) // 模拟数据，默认为视频类型
        return cell
    }
    
    func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
        delegate?.videoCollectionCell(self, didSelectItemAt: indexPath.item, title: sectionTitle)
    }
}
