//
//  AccountPrivacyViewController.swift
//  Shuxia<PERSON>qi
//
//  Created by yong<PERSON>ng ye on 2025/3/20.
//

//隐私中心
import UIKit
import SnapKit
import AVFoundation
import Photos
import CoreLocation

// 权限管理工具类
class PermissionManager {

    // 获取相机权限状态
    static func getCameraPermissionStatus() -> String {
        let status = AVCaptureDevice.authorizationStatus(for: .video)
        switch status {
        case .authorized:
            return "已授权"
        case .denied, .restricted:
            return "已拒绝"
        case .notDetermined:
            return "去设置"
        @unknown default:
            return "未知"
        }
    }

    // 获取麦克风权限状态
    static func getMicrophonePermissionStatus() -> String {
        let status = AVCaptureDevice.authorizationStatus(for: .audio)
        switch status {
        case .authorized:
            return "已授权"
        case .denied, .restricted:
            return "已拒绝"
        case .notDetermined:
            return "去设置"
        @unknown default:
            return "未知"
        }
    }

    // 获取相册权限状态
    static func getPhotoPermissionStatus() -> String {
        let status: PHAuthorizationStatus
        if #available(iOS 14, *) {
            status = PHPhotoLibrary.authorizationStatus(for: .readWrite)
        } else {
            status = PHPhotoLibrary.authorizationStatus()
        }

        switch status {
        case .authorized:
            return "已授权"
        case .limited:
            return "部分允许"
        case .denied, .restricted:
            return "已拒绝"
        case .notDetermined:
            return "去设置"
        @unknown default:
            return "未知"
        }
    }

    // 获取位置权限状态
    static func getLocationPermissionStatus() -> String {
        let status = CLLocationManager.authorizationStatus()
        switch status {
        case .authorizedWhenInUse:
            return "使用时允许"
        case .authorizedAlways:
            return "始终允许"
        case .denied, .restricted:
            return "已拒绝"
        case .notDetermined:
            return "去设置"
        @unknown default:
            return "未知"
        }
    }
}

// 隐私设置项模型
struct PrivacyItem {
    let icon: String // 图标名称
    let title: String // 标题
    let rightText: String? // 右侧文本（可选）
    let showArrow: Bool // 是否显示箭头
    let action: (() -> Void)? // 点击操作
}

// 隐私设置分组模型
struct PrivacySection {
    let title: String? // 分组标题
    let items: [PrivacyItem]
    let showFooter: Bool // 是否在分组底部显示间隔
}

class AccountPrivacyViewController: BaseViewController, UITableViewDelegate, UITableViewDataSource {
    
    // 表格视图
    private lazy var tableView: UITableView = {
        let tableView = UITableView(frame: .zero, style: .grouped)
        tableView.delegate = self
        tableView.dataSource = self
        tableView.backgroundColor = UIColor(hex: "#F5F5F5")
        tableView.separatorStyle = .none
        tableView.register(PrivacyCell.self, forCellReuseIdentifier: "PrivacyCell")
        tableView.contentInsetAdjustmentBehavior = .never
        // 设置表格视图的内边距
        tableView.contentInset = UIEdgeInsets(top: 12, left: 0, bottom: 12, right: 0)
        return tableView
    }()
    
    // 设置数据
    private var sections: [PrivacySection] = []
    
    // 新增：缓存最近一次接口返回的互动管理数据
    private var lastInteractionData: [UserInteractionSetItem]? = nil

    // 新增：缓存黑名单用户数量
    private var blacklistUserCount: Int = 0
    
    override func viewDidLoad() {
        super.viewDidLoad()

        // 设置标题
        navTitle = "隐私中心"

        // 设置视图背景色
        view.backgroundColor = .white

        // 设置表格视图
        setupTableView()

        // 设置数据
        fetchPrivacyData()
    }

    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)

        // 每次页面即将显示时刷新权限状态和黑名单数量
        refreshPermissionStatus()
        refreshBlacklistCount()
    }
    
    private func setupTableView() {
        // 添加表格视图到内容视图
        contentView.addSubview(tableView)

        // 设置表格视图约束
        tableView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
    }

    // 刷新权限状态
    private func refreshPermissionStatus() {
        // 找到系统权限分组并更新权限状态
        guard sections.count > 0 else { return }

        // 查找系统权限分组的索引
        var systemPermissionSectionIndex = -1
        for (index, section) in sections.enumerated() {
            if section.title == "系统权限" {
                systemPermissionSectionIndex = index
                break
            }
        }

        guard systemPermissionSectionIndex >= 0 else { return }

        // 更新系统权限分组的数据
        let updatedItems = [
            PrivacyItem(icon: "privacy_camera", title: "相机权限", rightText: PermissionManager.getCameraPermissionStatus(), showArrow: true, action: { [weak self] in
                self?.navigateToCameraSettings()
            }),
            PrivacyItem(icon: "privacy_location", title: "位置权限", rightText: PermissionManager.getLocationPermissionStatus(), showArrow: true, action: { [weak self] in
                self?.navigateToLocationSettings()
            }),
            PrivacyItem(icon: "privacy_microphone", title: "麦克风权限", rightText: PermissionManager.getMicrophonePermissionStatus(), showArrow: true, action: { [weak self] in
                self?.navigateToMicrophoneSettings()
            }),
            PrivacyItem(icon: "privacy_photos", title: "相册/存储权限", rightText: PermissionManager.getPhotoPermissionStatus(), showArrow: true, action: { [weak self] in
                self?.navigateToPhotoSettings()
            })
        ]

        let updatedSection = PrivacySection(title: "系统权限", items: updatedItems, showFooter: false)
        sections[systemPermissionSectionIndex] = updatedSection

        // 刷新表格视图
        tableView.reloadData()
    }

    // 新增：刷新黑名单用户数量
    private func refreshBlacklistCount() {
        APIManager.shared.getSvUserBlackCount(page: 1, size: 10) { [weak self] result in
            guard let self = self else { return }
            DispatchQueue.main.async {
                switch result {
                case .success(let response):
                    if response.status == 200, let count = response.data {
                        let newCount = count
                        if newCount != self.blacklistUserCount {
                            self.blacklistUserCount = newCount
                            self.updateBlacklistSection()
                        }
                    }
                case .failure(_):
                    // 静默处理失败情况，不影响用户体验
                    break
                }
            }
        }
    }

    // 新增：更新黑名单管理分组
    private func updateBlacklistSection() {
        guard !sections.isEmpty else { return }

        let blacklistRightText = "\(blacklistUserCount)个用户"
        let updatedItem = PrivacyItem(icon: "privacy_blacklist", title: "黑名单管理", rightText: blacklistRightText, showArrow: true, action: { [weak self] in
            self?.navigateToBlacklist()
        })

        let updatedSection = PrivacySection(title: nil, items: [updatedItem], showFooter: true)
        sections[0] = updatedSection

        // 只刷新第一个分组
        tableView.reloadSections(IndexSet(integer: 0), with: .none)
    }
    
    // 新增：加载数据方法，包含接口请求
    private func fetchPrivacyData() {
        // 先获取黑名单用户数量
        fetchBlacklistCount { [weak self] in
            self?.setupPrivacySections()
        }
    }

    // 新增：获取黑名单用户数量
    private func fetchBlacklistCount(completion: @escaping () -> Void) {
        APIManager.shared.getSvUserBlackCount(page: 1, size: 10) { [weak self] result in
            guard let self = self else { return }
            DispatchQueue.main.async {
                switch result {
                case .success(let response):
                    if response.status == 200, let count = response.data {
                        self.blacklistUserCount = count
                    } else {
                        self.blacklistUserCount = 0
                    }
                case .failure(_):
                    self.blacklistUserCount = 0
                }
                completion()
            }
        }
    }

    // 新增：设置隐私设置分组
    private func setupPrivacySections() {
        // 第一组：黑名单管理（显示用户数量）
        let blacklistRightText = "\(blacklistUserCount)个用户"
        let section1 = PrivacySection(title: nil, items: [
            PrivacyItem(icon: "privacy_blacklist", title: "黑名单管理", rightText: blacklistRightText, showArrow: true, action: { [weak self] in
                self?.navigateToBlacklist()
            })
        ], showFooter: true)

        // 第三组：系统权限（显示实际权限状态）
        let section3 = PrivacySection(title: "系统权限", items: [
            PrivacyItem(icon: "privacy_camera", title: "相机权限", rightText: PermissionManager.getCameraPermissionStatus(), showArrow: true, action: { [weak self] in
                self?.navigateToCameraSettings()
            }),
            PrivacyItem(icon: "privacy_location", title: "位置权限", rightText: PermissionManager.getLocationPermissionStatus(), showArrow: true, action: { [weak self] in
                self?.navigateToLocationSettings()
            }),
            PrivacyItem(icon: "privacy_microphone", title: "麦克风权限", rightText: PermissionManager.getMicrophonePermissionStatus(), showArrow: true, action: { [weak self] in
                self?.navigateToMicrophoneSettings()
            }),
            PrivacyItem(icon: "privacy_photos", title: "相册/存储权限", rightText: PermissionManager.getPhotoPermissionStatus(), showArrow: true, action: { [weak self] in
                self?.navigateToPhotoSettings()
            })
        ], showFooter: false)

        APIManager.shared.getUserInteractionSet { [weak self] result in
            guard let self = self else { return }
            DispatchQueue.main.async {
                switch result {
                case .success(let response):
                    if response.isSuccess, let data = response.data {
                        // 组装互动管理分组
                        let interactionItems = data.map { item in
                            PrivacyItem(
                                icon: item.icon ?? "privacy_default",
                                title: item.name ?? "",
                                rightText: self.rightTextForState(item.state),
                                showArrow: true,
                                action: { [weak self] in
                                    self?.handleInteractionItemTap(item: item)
                                }
                            )
                        }
                        let section2 = PrivacySection(title: "互动管理", items: interactionItems, showFooter: true)
                        self.sections = [section1, section2, section3]
                        self.tableView.reloadData()
                        self.lastInteractionData = data
                    } else {
                        self.showToast(response.displayMessage)
                        // 只展示静态分组
                        self.sections = [section1, section3]
                        self.tableView.reloadData()
                    }
                case .failure(let error):
                    self.showToast(error.localizedDescription)
                    // 只展示静态分组
                    self.sections = [section1, section3]
                    self.tableView.reloadData()
                }
            }
        }
    }
    
    // 辅助：根据state返回右侧文案
    private func rightTextForState(_ state: Int?) -> String {
        switch state {
        case 0: return "所有人"
        case 1: return "我关注的"
        case 2: return "互相关注"
        case 3: return "不接收"
        default: return ""
        }
    }
    
    // 辅助：处理互动项点击
    private func handleInteractionItemTap(item: UserInteractionSetItem) {
        // 弹窗选项
        let options = ["所有人", "我关注的", "互相关注", "不接收"]
        // 动态获取当前cell的最新state
        let selectedIdx: Int = {
            // 通过title和icon唯一定位cell
            guard let section2 = sections.count >= 3 ? sections[1] : nil else { return item.state ?? 0 }
            if let matched = section2.items.first(where: { $0.title == item.name && $0.icon == (item.icon ?? "") }) {
                if let idx = options.firstIndex(of: matched.rightText ?? "") {
                    return idx
                }
            }
            return item.state ?? 0
        }()
        let sheet = NotificationPopSelector(options: options, selectedIndex: selectedIdx) { [weak self] idx in
            guard let self = self else { return }
            // 1. 调用设置API
            guard let interactionConfigId = item.id else { return }
            APIManager.shared.setUserInteractionSet(interactionConfigId: interactionConfigId, interactiveSettingButton: idx) { [weak self] result in
                guard let self = self else { return }
                DispatchQueue.main.async {
                    switch result {
                    case .success(let response):
                        if response.isSuccess {
                            // 2. 更新本地数据并刷新cell右侧文案
                            self.updateInteractionItemState(id: interactionConfigId, newState: idx)
                        } else {
                            self.showToast(response.displayMessage)
                        }
                    case .failure(let error):
                        self.showToast(error.localizedDescription)
                    }
                }
            }
        }
        if let window = UIApplication.shared.windows.first(where: { $0.isKeyWindow }) {
            sheet.show(in: window)
        }
    }
    
    // 新增：更新本地互动管理分组的state并刷新UI
    private func updateInteractionItemState(id: Int, newState: Int) {
        // 找到互动管理分组
        guard sections.count >= 3 else { return }
        var section2 = sections[1]
        var newItems: [PrivacyItem] = []
        for item in section2.items {
            if let iconUrl = URL(string: item.icon), iconUrl.scheme == "http" || iconUrl.scheme == "https" {
                // 网络icon，需通过id判断
                if let interactionId = extractInteractionId(from: item), interactionId == id {
                    let updatedItem = PrivacyItem(icon: item.icon, title: item.title, rightText: self.rightTextForState(newState), showArrow: item.showArrow, action: item.action)
                    newItems.append(updatedItem)
                } else {
                    newItems.append(item)
                }
            } else {
                newItems.append(item)
            }
        }
        section2 = PrivacySection(title: section2.title, items: newItems, showFooter: section2.showFooter)
        sections[1] = section2
        tableView.reloadData()
    }
    
    // 新增：从PrivacyItem中提取id（通过title和icon唯一性匹配原始数据）
    private func extractInteractionId(from item: PrivacyItem) -> Int? {
        // 由于PrivacyItem没有id字段，这里通过当前接口数据缓存匹配
        guard let data = lastInteractionData else { return nil }
        return data.first(where: { $0.name == item.title && $0.icon == item.icon })?.id
    }
    
    // MARK: - UITableViewDataSource
    
    func numberOfSections(in tableView: UITableView) -> Int {
        return sections.count
    }
    
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return sections[section].items.count
    }
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        let cell = tableView.dequeueReusableCell(withIdentifier: "PrivacyCell", for: indexPath) as! PrivacyCell
        let item = sections[indexPath.section].items[indexPath.row]
        cell.configure(with: item)
        return cell
    }
    
    // MARK: - UITableViewDelegate
    
    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        tableView.deselectRow(at: indexPath, animated: true)
        
        let item = sections[indexPath.section].items[indexPath.row]
        item.action?()
    }
    
    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        return 56 // 设置单元格高度为56
    }
    
    func tableView(_ tableView: UITableView, heightForHeaderInSection section: Int) -> CGFloat {
        return sections[section].title == nil ? 0 : 40 // 如果有标题，则高度为40，否则为0
    }
    
    func tableView(_ tableView: UITableView, heightForFooterInSection section: Int) -> CGFloat {
        return sections[section].showFooter ? 12 : 0.01 // 如果显示尾部，高度为12，否则为0.01（不能为0）
    }
    
    func tableView(_ tableView: UITableView, viewForHeaderInSection section: Int) -> UIView? {
        if sections[section].title == nil {
            return nil // 如果没有标题，返回nil
        }
        
        let headerView = UIView()
        headerView.backgroundColor = UIColor(hex: "#F5F5F5")
        
        let titleLabel = UILabel()
        titleLabel.text = sections[section].title
        titleLabel.font = UIFont.systemFont(ofSize: 18, weight: .bold)
        titleLabel.textColor = UIColor(hex: "#333333")
        
        headerView.addSubview(titleLabel)
        titleLabel.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(16)
            make.centerY.equalToSuperview()
        }
        
        return headerView
    }
    
    func tableView(_ tableView: UITableView, viewForFooterInSection section: Int) -> UIView? {
        if !sections[section].showFooter {
            return nil // 如果不显示尾部，返回nil
        }
        
        let footerView = UIView()
        footerView.backgroundColor = UIColor(hex: "#F5F5F5") // 设置背景色为浅灰色
        return footerView
    }
    
    // MARK: - Navigation Methods
    
    private func navigateToBlacklist() {
        print("导航到黑名单管理页面")
        // 实现导航到黑名单管理页面的逻辑
        
        let blacklistViewController = BlacklistManagerViewController()
        navigationController?.pushViewController(blacklistViewController, animated: true)
    }
    
    private func navigateToViewDynamicsSettings() {
        print("导航到动态可见性设置页面")
        // 实现导航到动态可见性设置页面的逻辑
    }
    
    private func navigateToMessageSettings() {
        print("导航到消息设置页面")
        // 实现导航到消息设置页面的逻辑
    }
    
    private func navigateToMentionSettings() {
        print("导航到@我设置页面")
        // 实现导航到@我设置页面的逻辑
    }
    
    private func navigateToCameraSettings() {
        print("导航到相机权限设置")
        // 实现导航到相机权限设置的逻辑
        openAppSettings()
    }
    
    private func navigateToLocationSettings() {
        print("导航到位置权限设置")
        // 实现导航到位置权限设置的逻辑
        openAppSettings()
    }
    
    private func navigateToMicrophoneSettings() {
        print("导航到麦克风权限设置")
        // 实现导航到麦克风权限设置的逻辑
        openAppSettings()
    }
    
    private func navigateToPhotoSettings() {
        print("导航到相册/存储权限设置")
        // 实现导航到相册/存储权限设置的逻辑
        openAppSettings()
    }
    
    // 打开应用设置页面
    private func openAppSettings() {
        if let url = URL(string: UIApplication.openSettingsURLString) {
            UIApplication.shared.open(url, options: [:], completionHandler: nil)
        }
    }
}

// MARK: - PrivacyCell

class PrivacyCell: UITableViewCell {
    
    // 容器视图
    private let containerView: UIView = {
        let view = UIView()
        view.backgroundColor = .white
        return view
    }()
    
    // 图标视图
    private let iconImageView: UIImageView = {
        let imageView = UIImageView()
        imageView.contentMode = .scaleAspectFit
        return imageView
    }()
    
    // 标题标签
    private let titleLabel: UILabel = {
        let label = UILabel()
        label.font = UIFont.systemFont(ofSize: 16)
        label.textColor = UIColor(hex: "#333333")
        return label
    }()
    
    // 右侧文本标签
    private let rightTextLabel: UILabel = {
        let label = UILabel()
        label.font = UIFont.systemFont(ofSize: 14)
        label.textColor = UIColor(hex: "#999999")
        label.textAlignment = .right
        return label
    }()
    
    // 箭头图标
    private let arrowImageView: UIImageView = {
        let imageView = UIImageView()
        imageView.image = UIImage(named: "setting_arrow")
        imageView.contentMode = .scaleAspectFit
        return imageView
    }()
    
    // 分隔线
    private let separatorLine: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor(hex: "#E7E7E7")
        return view
    }()
    
    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        setupUI()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    private func setupUI() {
        // 设置背景色
        backgroundColor = UIColor.clear
        selectionStyle = .none
        
        // 添加容器视图
        contentView.addSubview(containerView)
        containerView.snp.makeConstraints { make in
            make.left.right.equalToSuperview()
            make.top.equalToSuperview()
            make.bottom.equalToSuperview()
        }
        
        // 添加图标视图
        containerView.addSubview(iconImageView)
        iconImageView.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(16)
            make.centerY.equalToSuperview()
            make.width.height.equalTo(24)
        }
        
        // 添加标题标签
        containerView.addSubview(titleLabel)
        titleLabel.snp.makeConstraints { make in
            make.left.equalTo(iconImageView.snp.right).offset(12)
            make.centerY.equalToSuperview()
        }
        
        // 添加箭头图标
        containerView.addSubview(arrowImageView)
        arrowImageView.snp.makeConstraints { make in
            make.right.equalToSuperview().offset(-16)
            make.centerY.equalToSuperview()
            make.width.equalTo(16)
            make.height.equalTo(16)
        }
        
        // 添加右侧文本标签
        containerView.addSubview(rightTextLabel)
        rightTextLabel.snp.makeConstraints { make in
            make.right.equalTo(arrowImageView.snp.left).offset(-8)
            make.centerY.equalToSuperview()
        }
        
        // 添加分隔线
        containerView.addSubview(separatorLine)
        separatorLine.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(52) // 与标题左对齐
            make.right.equalToSuperview()
            make.bottom.equalToSuperview()
            make.height.equalTo(0.5)
        }
    }
    
    func configure(with item: PrivacyItem) {
        // 设置图标
        if let url = URL(string: item.icon), url.scheme == "http" || url.scheme == "https" {
#if canImport(Kingfisher)
            iconImageView.kf.setImage(with: url, placeholder: UIImage(named: "privacy_default"))
#else
            iconImageView.image = UIImage(named: "privacy_default")
#endif
        } else {
            iconImageView.image = UIImage(named: item.icon)
        }
        // 设置标题
        titleLabel.text = item.title
        // 设置右侧文本
        rightTextLabel.text = item.rightText
        rightTextLabel.isHidden = item.rightText == nil
        // 设置右侧文本颜色
        // if item.rightText == "去设置" {
        //     rightTextLabel.textColor = UIColor(hex: "#999999") // 灰色
        // } else if item.rightText == "已允许" || item.rightText == "使用时允许" || item.rightText == "始终允许" || item.rightText == "部分允许" {
        //     rightTextLabel.textColor = UIColor(hex: "#4CAF50") // 绿色表示已允许
        // } else if item.rightText == "已拒绝" {
        //     rightTextLabel.textColor = UIColor(hex: "#F44336") // 红色表示已拒绝
        // } else if item.rightText == "未设置" {
        //     rightTextLabel.textColor = UIColor(hex: "#FF9800") // 橙色表示未设置
        // } else {
            rightTextLabel.textColor = UIColor(hex: "#777777") // 黑色
        // }
        // 设置箭头
        arrowImageView.isHidden = !item.showArrow
        // 设置最后一个单元格的分隔线
        let isLastCell = item.title == "相册/存储权限" || item.title == "谁可以@我" || item.title == "黑名单管理"
        separatorLine.isHidden = isLastCell
    }
}

