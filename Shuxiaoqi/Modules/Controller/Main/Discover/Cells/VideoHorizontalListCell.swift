import UIKit

protocol VideoHorizontalListCellDelegate: AnyObject {
    func videoHorizontalListCell(_ cell: VideoHorizontalListCell, didSelectVideo video: VideoItem, inGroup group: VideoGroup)
}

class VideoHorizontalListCell: UICollectionViewCell {
    weak var delegate: VideoHorizontalListCellDelegate?
    private var videos: [VideoItem] = []
    private var group: VideoGroup?
    private var collectionView: UICollectionView!
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupCollectionView()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    private func setupCollectionView() {
        let layout = UICollectionViewFlowLayout()
        layout.scrollDirection = .horizontal
        layout.itemSize = CGSize(width: 221, height: 391)
        layout.minimumLineSpacing = 12
        layout.sectionInset = UIEdgeInsets(top: 0, left: 16, bottom: 0, right: 16)
        collectionView = UICollectionView(frame: .zero, collectionViewLayout: layout)
        collectionView.backgroundColor = .clear
        collectionView.showsHorizontalScrollIndicator = false
        collectionView.dataSource = self
        collectionView.delegate = self
        collectionView.register(VideoItemCell.self, forCellWithReuseIdentifier: "VideoItemCell")
        contentView.addSubview(collectionView)
        collectionView.translatesAutoresizingMaskIntoConstraints = false
        NSLayoutConstraint.activate([
            collectionView.topAnchor.constraint(equalTo: contentView.topAnchor),
            collectionView.leadingAnchor.constraint(equalTo: contentView.leadingAnchor),
            collectionView.trailingAnchor.constraint(equalTo: contentView.trailingAnchor),
            collectionView.bottomAnchor.constraint(equalTo: contentView.bottomAnchor)
        ])
    }
    
    func configure(with videos: [VideoItem], group: VideoGroup) {
        self.videos = videos
        self.group = group
        collectionView.reloadData()
    }

    /// 将内部横向列表滚动到首项
    func scrollToFirst(animated: Bool = true) {
        collectionView.setContentOffset(.zero, animated: animated)
    }

    // 当单元格被重用时，确保内部横向列表回到起始位置，
    // 避免用户横滑后滚动到屏幕外再返回时仍停留在之前的位置。
    override func prepareForReuse() {
        super.prepareForReuse()
        // 立即复位到左侧，无动画可保持复用流畅
        scrollToFirst(animated: false)
    }
}

extension VideoHorizontalListCell: UICollectionViewDataSource, UICollectionViewDelegate {
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        return videos.count
    }
    
    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        let cell = collectionView.dequeueReusableCell(withReuseIdentifier: "VideoItemCell", for: indexPath) as! VideoItemCell
        let video = videos[indexPath.item]
        let title = video.worksTitle ?? ""
        var cover = video.worksCoverImg ?? ""
        if !cover.isEmpty && !cover.hasPrefix("http") {
            cover = "https://test-youshu.gzyoushu.com/video" + cover
        }
        // 获取作者名称和头像
        let authorName = video.svUserMainVo?.customerName
        let avatarUrl = video.svUserMainVo?.wxAvator
        
        // 获取视频时长和播放时间
        let videoDuration = Float(video.duration ?? 0)
        
        // 这里我们可以随机生成一个播放进度，实际应用中应该从播放器获取
        // 在实际应用中，应该从TXVodPlayer获取当前播放时间
        let randomProgress = Float.random(in: 0.1...0.9)
        let currentPlayTime = videoDuration * randomProgress
        
        cell.configure(with: title, imageName: cover, authorName: authorName, avatarUrl: avatarUrl, isLive: group?.isLive ?? false, videoDuration: videoDuration, currentPlayTime: currentPlayTime, worksType: video.worksType)
        return cell
    }
    
    func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
        guard let group = group else { return }
        let video = videos[indexPath.item]
        delegate?.videoHorizontalListCell(self, didSelectVideo: video, inGroup: group)
    }
}
