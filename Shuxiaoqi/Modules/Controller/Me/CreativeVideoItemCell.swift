//
//  CreativeVideoItemCell.swift
//  Shuxiaoqi
//
//  Created by yongsheng ye on 2025/5/26.
//

import UIKit
import SnapKit
import Kingfisher

// MARK: - 视频项目单元格
class CreativeVideoItemCell: UITableViewCell {
    
    // MARK: - 回调闭包
    /// 编辑按钮点击回调
    var onEditTapped: (() -> Void)?
    /// 删除按钮点击回调
    var onDeleteTapped: (() -> Void)?
    
    private let thumbnailImageView: UIImageView = {
        let imageView = UIImageView()
        imageView.contentMode = .scaleAspectFill
        imageView.clipsToBounds = true
        imageView.backgroundColor = UIColor(hex: "#F0F0F0")
        imageView.layer.cornerRadius = 4
        return imageView
    }()
    
    private let titleLabel: UILabel = {
        let label = UILabel()
        label.text = "请添加标题"
        label.font = UIFont.systemFont(ofSize: 15, weight: .bold)
        label.textColor = UIColor(hex: "#333333")
        label.numberOfLines = 2
        return label
    }()
    
    private let durationLabel: UILabel = {
        let label = UILabel()
        label.font = UIFont.systemFont(ofSize: 12)
        label.textColor = UIColor(hex: "#379EFF")
        return label
    }()
    
    private let sizeLabel: UILabel = {
        let label = UILabel()
        label.font = UIFont.systemFont(ofSize: 12)
        label.textColor = UIColor(hex: "#379EFF")
        return label
    }()
    
    private let timeAgoLabel: UILabel = {
        let label = UILabel()
        label.font = UIFont.systemFont(ofSize: 12)
        label.textColor = UIColor(hex: "#777777")
        label.textAlignment = .right
        return label
    }()
    
    private let editButton: UIButton = {
        let button = UIButton(type: .custom)
        button.setTitle("编辑", for: .normal)
        button.setTitleColor(UIColor(hex: "#007AFF"), for: .normal)
        button.titleLabel?.font = UIFont.systemFont(ofSize: 12)
        button.backgroundColor = .white
        button.layer.cornerRadius = 12
        button.layer.borderWidth = 1
        button.layer.borderColor = UIColor(hex: "#007AFF").cgColor
        return button
    }()
    
    private let deleteButton: UIButton = {
        let button = UIButton(type: .custom)
        button.setTitle("删除", for: .normal)
        button.setTitleColor(UIColor(hex: "#FF3B30"), for: .normal)
        button.titleLabel?.font = UIFont.systemFont(ofSize: 12)
        button.backgroundColor = .white
        button.layer.cornerRadius = 12
        button.layer.borderWidth = 1
        button.layer.borderColor = UIColor(hex: "#FF3B30").cgColor
        return button
    }()
    
    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        self.selectionStyle = .none
        setupUI()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    private func setupUI() {
        self.backgroundColor = UIColor(hex: "#F5F5F5")
        // 添加白色背景卡片视图
        let cardView = UIView()
        cardView.backgroundColor = .white
        cardView.layer.cornerRadius = 16
        cardView.layer.shadowColor = UIColor.black.withAlphaComponent(0.05).cgColor
        cardView.layer.shadowOffset = CGSize(width: 0, height: 1)
        cardView.layer.shadowRadius = 3
        cardView.layer.shadowOpacity = 1
        
        contentView.addSubview(cardView)
        cardView.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(6)
            make.right.equalToSuperview().offset(-6)
            make.top.equalToSuperview().offset(5)
//            make.bottom.equalToSuperview().offset(-5)
            make.height.equalTo(120)
        }
        
        // 将所有元素添加到卡片上而不是直接添加到 contentView
        cardView.addSubview(thumbnailImageView)
        cardView.addSubview(titleLabel)
        cardView.addSubview(durationLabel)
        cardView.addSubview(sizeLabel)
        cardView.addSubview(timeAgoLabel)
        cardView.addSubview(editButton)
        cardView.addSubview(deleteButton)
        
        // 调整约束，使用相对于 cardView 的约束而不是 contentView
        thumbnailImageView.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(12)
            make.centerY.equalToSuperview()
            make.width.equalTo(60)
            make.height.equalTo(100)
        }
        
        titleLabel.snp.makeConstraints { make in
            make.top.equalTo(thumbnailImageView)
            make.left.equalTo(thumbnailImageView.snp.right).offset(16)
            make.right.equalToSuperview().offset(-12)
        }
        
        durationLabel.snp.makeConstraints { make in
            make.left.equalTo(titleLabel)
            make.top.equalTo(titleLabel.snp.bottom).offset(4)
        }
        
        sizeLabel.snp.makeConstraints { make in
            make.left.equalTo(durationLabel.snp.right).offset(10)
            make.centerY.equalTo(durationLabel)
        }
        
        timeAgoLabel.snp.makeConstraints { make in
            make.left.equalTo(titleLabel)
            make.bottom.equalTo(thumbnailImageView.snp.bottom)
        }
        
        deleteButton.snp.makeConstraints { make in
            make.right.equalToSuperview().offset(-12)
            make.bottom.equalTo(thumbnailImageView)
            make.width.equalTo(48)
            make.height.equalTo(32)
        }
        
        editButton.snp.makeConstraints { make in
            make.right.equalTo(deleteButton.snp.left).offset(-8)
            make.centerY.equalTo(deleteButton)
            make.width.equalTo(48)
            make.height.equalTo(32)
        }
        
        // 隐藏编辑按钮（当前版本已取消编辑功能）
        editButton.isHidden = true
        // 添加按钮点击事件
        editButton.addTarget(self, action: #selector(editButtonTapped), for: .touchUpInside)
        deleteButton.addTarget(self, action: #selector(deleteButtonTapped), for: .touchUpInside)
        
        // 移除分隔线，因为我们现在使用卡片式布局
    }
    
    func configure(title: String, duration: String, timeAgo: String, thumbnail: String, sizeInBytes: Int, showDuration: Bool = true) {
        if title == "" {
            titleLabel.text = "请添加标题"
        } else {
            titleLabel.text = title
        }
        
        // 根据作品类型决定是否显示时长
        if showDuration {
            durationLabel.isHidden = false
            durationLabel.text = "时长:\(duration)"
            // 当显示时长时，大小位于时长标签右侧
            sizeLabel.snp.remakeConstraints { make in
                make.left.equalTo(durationLabel.snp.right).offset(10)
                make.centerY.equalTo(durationLabel)
            }
        } else {
            durationLabel.isHidden = true
            // 当不显示时长（如笔记类型）时，让大小标签占据时长标签原位置
            sizeLabel.snp.remakeConstraints { make in
                make.left.equalTo(titleLabel)
                make.top.equalTo(titleLabel.snp.bottom).offset(4)
            }
        }

        timeAgoLabel.text = timeAgo
        // 新增: 如果时间未知则隐藏标签
        timeAgoLabel.isHidden = (timeAgo == "未知时间")

        sizeLabel.text = "大小: \(formatSize(sizeInBytes))"
        
        sizeLabel.isHidden = (formatSize(sizeInBytes) == "0.00 MB")
        
        // 设置封面图片
        if !thumbnail.isEmpty {
            if let url = URL(string: thumbnail) {
                // 使用 Kingfisher 加载图片
                thumbnailImageView.kf.setImage(
                    with: url,
                    placeholder: UIImage(named: "default_thumbnail"),
                    options: [
                        .transition(.fade(0.2)),
                        .cacheOriginalImage
                    ]
                )
            }
        } else {
            thumbnailImageView.image = UIImage(named: "default_thumbnail")
        }
    }
    
    private func formatSize(_ bytes: Int) -> String {
        let mb = Double(bytes) / 1024.0 / 1024.0
        return String(format: "%.2f MB", mb)
    }
    
    @objc private func editButtonTapped() {
        // 处理编辑按钮点击
        print("编辑按钮点击 - 标题: \(titleLabel.text ?? "")")
        onEditTapped?()
    }
    
    @objc private func deleteButtonTapped() {
        // 处理删除按钮点击
        print("删除按钮点击 - 标题: \(titleLabel.text ?? "")")
        onDeleteTapped?()
    }
}
