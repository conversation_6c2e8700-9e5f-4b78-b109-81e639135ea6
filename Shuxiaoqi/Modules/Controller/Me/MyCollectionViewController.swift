//
//  MyCollectionViewController.swift
//  Shuxia<PERSON>qi
//
//  Created by yongsheng ye on 2025/3/20.
//

//  我的收藏
import UIKit
import JXSegmentedView
import SnapKit
import MJRefresh
import SmartCodable

class MyCollectionViewController: BaseViewController {
    
    // MARK: - 属性
    
    // 是否处于编辑模式
    private var isEditingMode: Bool = false {
        didSet {
            updateEditModeUI()
            // 通知子视图控制器编辑状态改变
            notifyChildViewControllersEditModeChanged()
        }
    }
    
    // 底部编辑操作视图
    let editActionsContentHeight: CGFloat = 80
    private lazy var editActionsView: UIView = {
        let view = UIView()
        view.backgroundColor = .white
        view.isHidden = true // 默认隐藏
        
        // 添加分割线
        let separator = UIView()
        separator.backgroundColor = UIColor(hex: "#EEEEEE")
        view.addSubview(separator)
        separator.snp.makeConstraints { make in
            make.top.left.right.equalToSuperview()
            make.height.equalTo(0.5)
        }
        
        // 内容容器，保证内容不会被安全区遮挡
        let contentContainer = UIView()
        contentContainer.backgroundColor = .clear
        view.addSubview(contentContainer)
        contentContainer.snp.makeConstraints { make in
            make.left.right.top.equalToSuperview()
            make.bottom.equalToSuperview().offset(-WindowUtil.safeAreaBottom)
        }
        
        // 添加全选按钮
        let selectAllBtn = UIButton(type: .custom)
        // 请确保有对应的图标资源 "icon_checkbox_unselected" 和 "icon_checkbox_selected"
        selectAllBtn.setImage(UIImage(named: "video_allbtn_default")?.withRenderingMode(.alwaysOriginal), for: .normal)
        selectAllBtn.setImage(UIImage(named: "video_allbtn_selected")?.withRenderingMode(.alwaysOriginal), for: .selected)
        selectAllBtn.setTitle("全选", for: .normal) // 文字前加空格以模拟间距
        selectAllBtn.setTitleColor(UIColor(hex: "#333333"), for: .normal)
        selectAllBtn.titleLabel?.font = UIFont.systemFont(ofSize: 14)
        selectAllBtn.addTarget(self, action: #selector(selectAllItems(_:)), for: .touchUpInside)
        selectAllBtn.contentHorizontalAlignment = .left
        // 调整内边距让图标和文字对齐，图标大小约为20x20
        // 这些值可能需要根据实际图标和字体微调
        selectAllBtn.imageEdgeInsets = UIEdgeInsets(top: 0, left: 0, bottom: 0, right: 0)
        selectAllBtn.titleEdgeInsets = UIEdgeInsets(top: 0, left: 8, bottom: 0, right: -8) // 文字向右移动8
        selectAllBtn.contentEdgeInsets = UIEdgeInsets(top: 0, left: 0, bottom: 0, right: 8) // 整体内容留出右边距以适应 titleEdgeInsets

        contentContainer.addSubview(selectAllBtn)
        selectAllBtn.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(12)
            make.centerY.equalToSuperview()
            make.height.equalTo(40) // 给按钮一个触摸高度
            // 宽度由内容决定
        }
        self.selectAllButton = selectAllBtn // 保存引用
        
        // 添加删除按钮
        let deleteBtn = UIButton(type: .custom) // 使用 custom 类型以设置背景色
        deleteBtn.setTitle("取消收藏", for: .normal)
        deleteBtn.setTitleColor(.white, for: .normal)
        deleteBtn.titleLabel?.font = UIFont.systemFont(ofSize: 14, weight: .medium)
        deleteBtn.backgroundColor = UIColor(hex: "#FF8F1F")
        deleteBtn.layer.cornerRadius = 5 // 圆角
        deleteBtn.addTarget(self, action: #selector(deleteSelectedItems), for: .touchUpInside)
        contentContainer.addSubview(deleteBtn)
        deleteBtn.snp.makeConstraints { make in
            make.right.equalToSuperview().offset(-12)
            make.centerY.equalToSuperview()
            make.width.equalTo(86)
            make.height.equalTo(32)
        }
        
        return view
    }()
    
    // 分段控制器数据源
    private lazy var segmentedDataSource: JXSegmentedTitleDataSource = {
        let dataSource = JXSegmentedTitleDataSource()
        dataSource.titles = ["内容", "商品", "店铺"]
        dataSource.titleSelectedColor = UIColor(hex: "#FF5936")
        dataSource.titleNormalColor = UIColor(hex: "#333333")
        dataSource.titleSelectedFont = UIFont.systemFont(ofSize: 16, weight: .medium)
        dataSource.titleNormalFont = UIFont.systemFont(ofSize: 16)
        dataSource.isTitleColorGradientEnabled = true
        
        // 设置为等宽模式
        dataSource.itemWidth = UIScreen.main.bounds.width / 3
        dataSource.itemSpacing = 0  // 设置项目间距为0，确保三个按钮刚好占满宽度
        
        return dataSource
    }()
    
    // 分段控制器
    private lazy var segmentedView: JXSegmentedView = {
        let segmentedView = JXSegmentedView()
        segmentedView.backgroundColor = .white
        segmentedView.dataSource = segmentedDataSource
        
        // 配置指示器
        let indicator = JXSegmentedIndicatorLineView()
        indicator.indicatorColor = UIColor(hex: "#FF5936")
        indicator.indicatorHeight = 2
        
        // 设置指示器宽度等于item宽度
        indicator.indicatorWidth = UIScreen.main.bounds.width / 3
        indicator.indicatorPosition = .bottom
        
        segmentedView.indicators = [indicator]
        
        segmentedView.delegate = self
        return segmentedView
    }()
    
    // 列表容器
    private lazy var listContainerView: JXSegmentedListContainerView = {
        let containerView = JXSegmentedListContainerView(dataSource: self)
        return containerView
    }()
    
    // 收藏内容数据（首次为空，待接口填充）
    private var contentCollections: [[VideoItem]] = []
    
    // 分区标题
    private let sectionTitles = ["今天", "昨天", "更早"]
    
    // 用于持有全选按钮的引用
    private var selectAllButton: UIButton?

    // MARK: - 生命周期
    
    override func viewDidLoad() {
        super.viewDidLoad()
        view.backgroundColor = .white
        navTitle = "收藏"
        
        // 使用 rightNavTitle 和 rightNavAction 设置按钮
        setupNavigationBarButton()
        setupUI()

        // 初始配置 segmentedView，只需要执行一次
        segmentedView.defaultSelectedIndex = 0
        segmentedView.reloadData()
    }
    
    override func viewDidLayoutSubviews() {
        super.viewDidLayoutSubviews()
        updateEditActionsViewConstraints()
    }
    
    // MARK: - UI 设置
    
    // 修改 setupNavigationBarButton 以使用 BaseViewController 的 rightNavTitle/Action
    private func setupNavigationBarButton() {
        // 设置初始标题和动作
        rightNavTitle = "管理"
        rightNavAction = #selector(toggleEditMode)
        // BaseViewController 会根据这两个属性创建和管理按钮
    }
    
    private func setupUI() {
        // BaseViewController 已经处理了 contentView 的添加和布局
        // 我们只需要将子视图添加到 BaseViewController 提供的 contentView 中
        
        // 添加分段控制器
        contentView.addSubview(segmentedView)
        segmentedView.snp.makeConstraints { make in
            make.top.left.right.equalToSuperview()
            make.height.equalTo(44)
        }
        
        // 添加底部操作视图
        // 注意：editActionsView 应该添加到 self.view 上，以便覆盖 contentView 和 TabBar
        view.addSubview(editActionsView) // 添加到 self.view
        view.bringSubviewToFront(editActionsView) // 确保在最上层
        
        // 添加内容容器
        contentView.addSubview(listContainerView)
        listContainerView.snp.makeConstraints { make in
            make.top.equalTo(segmentedView.snp.bottom)
            make.left.right.bottom.equalToSuperview() // 填充 contentView 的剩余空间
        }
        
        // 关联分段控制器和列表容器
        segmentedView.listContainer = listContainerView
    }
    
    // MARK: - 编辑模式逻辑
    
    @objc private func toggleEditMode() {
        isEditingMode.toggle()
        
        // 1. 根据编辑状态设置按钮颜色
        rightNavButtonTintColor = isEditingMode ? UIColor(hex: "#C4C4C4") : UIColor(hex: "#333333")
        
        // 2. 设置按钮标题，触发 BaseViewController 更新按钮
        // BaseViewController 的 didSet 会使用上面设置的 rightNavButtonTintColor
        rightNavTitle = isEditingMode ? "取消" : "管理"
        
        // 3. 如果退出编辑模式，重置全选按钮状态
        if !isEditingMode {
            selectAllButton?.isSelected = false
        }
    }
    
    // 修改 updateEditModeUI 以更新 UIButton 的标题和 editActionsView 的位置
    private func updateEditModeUI() {
        // 更新按钮标题的操作已移至 toggleEditMode
        
        // 控制底部视图的显示/隐藏和位置
        editActionsView.isHidden = false // 先设置为可见，通过动画移动
        updateEditActionsViewConstraints()

        // 更新列表容器的底部约束以避开 editActionsView
        listContainerView.snp.remakeConstraints { make in
             make.top.equalTo(segmentedView.snp.bottom)
             make.left.right.equalToSuperview()
             // 根据编辑模式调整底部约束
             if isEditingMode {
                 // contentView 的底部需要给 editActionsView 留出空间
                 make.bottom.equalToSuperview().offset(-editActionsContentHeight) // 使用内容高度作为偏移
             } else {
                 make.bottom.equalToSuperview() // 非编辑模式时占满 contentView
             }
         }

        // 添加动画使切换更平滑
        UIView.animate(withDuration: 0.3, animations: {
            self.view.layoutIfNeeded() // 动画约束变化
        }) { finished in
            // 动画结束后，如果不是编辑模式，可以再次隐藏底部视图
            if !self.isEditingMode {
                self.editActionsView.isHidden = true
            }
        }
    }
    
    // 通知子视图控制器编辑状态改变
    private func notifyChildViewControllersEditModeChanged() {
        // JXSegmentedListContainerView 没有直接提供访问已加载列表的方法
        // 我们可以在 listContainerView(_:initListAt:) 中存储实例，或者找到当前显示的列表
        guard let currentList = listContainerView.validListDict[segmentedView.selectedIndex] as? BaseEditModeListViewController else {
            // 如果当前列表不是我们期望的类型或者未加载，可以考虑遍历 validListDict
             print("当前列表无法响应该编辑模式切换，或尚未加载。")
             // 遍历所有已加载的列表并尝试更新
             listContainerView.validListDict.values.forEach { list in
                 if let editableList = list as? BaseEditModeListViewController {
                     editableList.setEditingMode(isEditingMode)
                 }
             }
             return
        }
        currentList.setEditingMode(isEditingMode)

        // 如果需要确保所有列表（即使未显示）都更新状态，需要迭代 validListDict
        // listContainerView.validListDict.values.forEach { list in
        //     if let editableList = list as? BaseEditModeListViewController {
        //         editableList.setEditingMode(isEditingMode)
        //     }
        // }
    }
    
    // 删除选中项 (待实现)
    // 旧版 deleteSelectedItems 已迁移至统一实现，避免重复定义
    // @objc private func deleteSelectedItems() { }

    // MARK: - Action Handlers
    @objc private func selectAllItems(_ sender: UIButton) {
        print("--- MyCollectionViewController: selectAllItems Tapped ---")
        guard let currentListVC = listContainerView.validListDict[segmentedView.selectedIndex] as? ContentCollectionListViewController else {
            print("Current list (\(segmentedView.selectedIndex)) does not support select all.")
            return
        }

        // 查询列表当前的全选状态来决定目标操作
        let isCurrentlyAllSelected = currentListVC.areAllItemsSelected()
        let targetState = !isCurrentlyAllSelected // 目标是反转当前状态
        print("--- MyCollectionViewController: List is currently allSelected=\(isCurrentlyAllSelected). Requesting setSelectAll(\(targetState)) ---")

        // 调用列表控制器的 setSelectAll，不再关心 completion handler 对按钮状态的影响
        currentListVC.setSelectAll(targetState) { actualStateAfterAttempt in
             print("--- MyCollectionViewController: setSelectAll completion finished. List reported state: \(actualStateAfterAttempt) ---")
             // 按钮状态的更新完全依赖 onSelectionChange 回调
        }
    }
}

// MARK: - JXSegmentedViewDelegate
extension MyCollectionViewController: JXSegmentedViewDelegate {
    func segmentedView(_ segmentedView: JXSegmentedView, didSelectedItemAt index: Int) {
        // 切换tab时，如果处于编辑模式，需要通知新的列表更新其状态
         if isEditingMode {
              // 延迟一点确保列表已初始化
              DispatchQueue.main.async { [weak self] in
                  guard let self = self, let newList = self.listContainerView.validListDict[index] as? BaseEditModeListViewController else {
                      return
                  }
                  newList.setEditingMode(self.isEditingMode)
                  
                  // 如果是内容列表，确保新显示的列表状态被通知，以更新按钮
                  if let contentList = newList as? ContentCollectionListViewController {
                      contentList.updateAndNotifySelectionState() // 主动触发一次状态检查和回调
                  } else {
                      // 其他列表类型可能不支持全选，重置按钮
                      print("--- MyCollectionViewController: Switched to non-content tab, resetting selectAllButton ---")
                      self.selectAllButton?.isSelected = false
                      self.selectAllButton?.setImage(UIImage(named: "video_allbtn_default")?.withRenderingMode(.alwaysOriginal), for: .normal)
                  }
              }
          }
    }
}

// MARK: - JXSegmentedListContainerViewDataSource
extension MyCollectionViewController: JXSegmentedListContainerViewDataSource {
    func numberOfLists(in listContainerView: JXSegmentedListContainerView) -> Int {
        return 3 // 内容、商品、店铺
    }
    
    func listContainerView(_ listContainerView: JXSegmentedListContainerView, initListAt index: Int) -> JXSegmentedListContainerViewListDelegate {
        let listVC: BaseEditModeListViewController // 使用基类引用
        switch index {
        case 0:
            // 传入数据和更新闭包
            let contentVC = ContentCollectionListViewController(sectionTitles: sectionTitles, collections: contentCollections) { [weak self] updatedData in
                 self?.contentCollections = updatedData
             }
            // 设置选中状态变化回调
            contentVC.onSelectionChange = { [weak self] hasSelection, allSelected in
                 print("--- MyCollectionViewController: onSelectionChange received: hasSelection=\(hasSelection), allSelected=\(allSelected) ---")
                 // --- Add check for selectAllButton reference ---
                 guard let self = self, let button = self.selectAllButton else {
                     print("--- MyCollectionViewController: Error - selectAllButton reference is nil in onSelectionChange callback! ---")
                     return
                 }
                 // 更新全选按钮状态属性，按钮会自动切换图片
                 button.isSelected = allSelected
                 print("--- MyCollectionViewController: Updated selectAllButton UI to isSelected=\(allSelected) ---")
                 // (可选) 更新删除按钮的启用状态
                 // self.deleteButton?.isEnabled = hasSelection
            }
            listVC = contentVC
        case 1:
            listVC = ProductsCollectionListViewController()
        case 2:
            listVC = ShopsCollectionListViewController()
        default:
             // 创建默认的内容列表或抛出错误
             let contentVC = ContentCollectionListViewController(sectionTitles: sectionTitles, collections: contentCollections) { [weak self] updatedData in
                 self?.contentCollections = updatedData
             }
             listVC = contentVC
        }
        // 在初始化时设置当前的编辑模式
        listVC.setEditingMode(isEditingMode)
        return listVC // 返回 JXSegmentedListContainerViewListDelegate 兼容的实例
    }
}

// MARK: - 为列表视图控制器定义一个基类或协议，以便统一处理编辑模式
// 该段代码在此前重构时被误删，现恢复。

protocol EditableListViewController: JXSegmentedListContainerViewListDelegate {
    /// 外部调用以切换列表的编辑模式
    func setEditingMode(_ isEditing: Bool)
}

/// 所有可编辑列表 VC 的基类，默认实现 JXSegmentedListContainerViewListDelegate
class BaseEditModeListViewController: UIViewController, EditableListViewController {
    /// 当前列表是否处于编辑状态
    var isListEditing: Bool = false

    /// 切换编辑模式，子类可重写 `updateUIForEditingMode` 实现具体 UI 行为
    func setEditingMode(_ isEditing: Bool) {
        isListEditing = isEditing
        updateUIForEditingMode(isEditing)
    }

    /// 提供默认空实现，供子类覆盖
    func updateUIForEditingMode(_ isEditing: Bool) {
        // 子类实现
    }

    /// JXSegmentedListContainerViewListDelegate 必需
    func listView() -> UIView {
        return view
    }
}

// MARK: - 内容收藏列表
class ContentCollectionListViewController: BaseEditModeListViewController {
    
    private var currentAlertView: CleanupConfirmAlertView?
    private var dimmingView: UIView?
    // 记录原始底部inset
    private var originalBottomInset: CGFloat = 0

    // 表格视图
    private lazy var tableView: UITableView = {
        let tableView = UITableView(frame: .zero, style: .grouped)
        tableView.delegate = self
        tableView.dataSource = self
        tableView.backgroundColor = UIColor(hex: "#F5F5F5")
        tableView.separatorStyle = .none
        tableView.showsVerticalScrollIndicator = false
        tableView.register(ContentCollectionCell.self, forCellReuseIdentifier: "ContentCollectionCell")
        tableView.register(CollectionSectionHeaderView.self, forHeaderFooterViewReuseIdentifier: "CollectionSectionHeaderView")
        tableView.contentInset = UIEdgeInsets(top: 10, left: 0, bottom: 20, right: 0)
        tableView.allowsMultipleSelectionDuringEditing = true // 如果需要TableView级别的编辑
        return tableView
    }()
    
    // 分区标题
    private let sectionTitles: [String]
    
    // 收藏数据
    var collections: [[VideoItem]]
    // 已选中的作品ID集合
    var selectedIds: Set<String> = []
    // 数据更新闭包，当选中状态或数据删除时回调
    private var onDataUpdate: (([[VideoItem]]) -> Void)?
    // 选中状态变化回调 (是否有选中项, 是否全部选中)
    var onSelectionChange: ((_ hasSelection: Bool, _ allSelected: Bool) -> Void)?

    init(sectionTitles: [String], collections: [[VideoItem]], onDataUpdate: (([[VideoItem]]) -> Void)? = nil) {
        self.sectionTitles = sectionTitles
        self.collections = collections
        self.onDataUpdate = onDataUpdate // 保存闭包
        super.init(nibName: nil, bundle: nil)
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        // 记录初始inset
        originalBottomInset = tableView.contentInset.bottom
        setupRefresh()
        fetchCollectionVideos(isRefresh: true)
    }
    
    private func setupUI() {
        view.addSubview(tableView)
        tableView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }

        // --- 设置 Table Footer View ---
//        let footerView = CleanupFooterView(frame: CGRect(x: 0, y: 0, width: view.bounds.width, height: 44)) // 使用 view.bounds.width
//        footerView.onCleanupTapped = { [weak self] in
//            print("--- ContentCollectionListVC: Cleanup action triggered! Showing alert... ---")
//            self?.showCleanupConfirmAlert()
//        }
//        tableView.tableFooterView = footerView
        // ---------------------------
    }
    
    // MARK: - Alert Presentation

    private func showCleanupConfirmAlert() {
        // Get the key window
        guard let window = (UIApplication.shared.connectedScenes.first as? UIWindowScene)?.windows.first(where: { $0.isKeyWindow }) else {
            print("Error: Could not find key window.")
            return
        }

        // Create dimming view
        dimmingView = UIView(frame: window.bounds) // Use window bounds
        dimmingView!.backgroundColor = UIColor.black.withAlphaComponent(0.4)
        dimmingView!.alpha = 0
        // --- Add Tap Gesture to Dimming View ---
        let tapGesture = UITapGestureRecognizer(target: self, action: #selector(dismissCleanupConfirmAlert))
        dimmingView!.addGestureRecognizer(tapGesture)
        dimmingView!.isUserInteractionEnabled = true // Make sure it can receive taps
        // -------------------------------------
        window.addSubview(dimmingView!) // Add to window

        // Create alert view
        currentAlertView = CleanupConfirmAlertView()
        currentAlertView!.alpha = 0
        currentAlertView!.transform = CGAffineTransform(scaleX: 1.1, y: 1.1) // Start slightly larger
        window.addSubview(currentAlertView!) // Add to window
        currentAlertView!.snp.makeConstraints { make in
            make.edges.equalTo(window) // Alert view itself fills the window
        }

        // Setup actions
        currentAlertView!.onCancel = { [weak self] in
            self?.dismissCleanupConfirmAlert()
        }
        currentAlertView!.onConfirm = { [weak self] in
            print("--- ContentCollectionListVC: Cleanup Confirmed! ---")
            // --- Implement actual cleanup logic here ---
            // 1. Filter self.collections to remove invalid items
            // 2. Call self.onDataUpdate?(filteredCollections)
            // 3. self.tableView.reloadData()
            // 4. self.updateAndNotifySelectionState() (if needed)
            // --------------------------------------------
            self?.dismissCleanupConfirmAlert()
        }

        // Animate presentation
        UIView.animate(withDuration: 0.3, delay: 0, options: .curveEaseOut, animations: {
            self.dimmingView?.alpha = 1
            self.currentAlertView?.alpha = 1
            self.currentAlertView?.transform = .identity // Animate to normal size
        })
    }

    @objc private func dismissCleanupConfirmAlert() {
        UIView.animate(withDuration: 0.2, delay: 0, options: .curveEaseIn, animations: {
            self.dimmingView?.alpha = 0
            self.currentAlertView?.alpha = 0
            self.currentAlertView?.transform = CGAffineTransform(scaleX: 0.9, y: 0.9) // Shrink slightly
        }) { [weak self] _ in
            self?.dimmingView?.removeFromSuperview()
            self?.currentAlertView?.removeFromSuperview()
            self?.dimmingView = nil
            self?.currentAlertView = nil
        }
    }
    
    // 重写基类方法以更新 TableView
    override func updateUIForEditingMode(_ isEditing: Bool) {
        super.updateUIForEditingMode(isEditing)
        // 更新底部 inset，但无需触发表格整体刷新
        tableView.contentInset.bottom = isEditing ? (originalBottomInset + WindowUtil.safeAreaBottom) : originalBottomInset
        
        if !isEditing {
            // 退出编辑模式，清空选中集合
            selectedIds.removeAll()
            updateAndNotifySelectionState()
        } else {
            // 进入编辑模式，确保初始状态被通知
            updateAndNotifySelectionState()
        }
        // 局部刷新当前可见 Cell，避免闪烁与额外网络请求
        refreshVisibleCellsEditingState()
        
        // 避免编辑模式切换引发 MJRefresh 自动加载造成多余接口请求
        if isEditing {
            // 隐藏 footer，防止触发 loadMore
            tableView.mj_footer?.isHidden = true
            tableView.mj_footer?.endRefreshing()
        } else {
            // 恢复 footer 显示（如果还有更多数据）
            tableView.mj_footer?.isHidden = !hasMore
        }
    }

    /// 刷新当前可见 Cell 的编辑状态和选中指示器，避免整表 reload
    private func refreshVisibleCellsEditingState() {
        for case let cell as ContentCollectionCell in tableView.visibleCells {
            cell.updateEditing(isListEditing, selectedIds: selectedIds)
        }
    }

    // 内部方法：取消所有项目的选中
    private func deselectAllItemsAndUpdateState() {
        if selectedIds.isEmpty { return }
        selectedIds.removeAll()
        refreshVisibleCellsEditingState()
        updateAndNotifySelectionState()
    }

    // 公开方法：设置全选/取消全选
    func setSelectAll(_ selectAll: Bool, completion: ((Bool) -> Void)? = nil) {
        // 收集所有 id
        var allIds: [String] = []
        for section in collections {
            for v in section {
                if let wid = v.worksIdString { allIds.append(wid) }
            }
        }
        if selectAll {
            selectedIds = Set(allIds)
        } else {
            selectedIds.removeAll()
        }
        refreshVisibleCellsEditingState()
        updateAndNotifySelectionState()
        completion?(selectAll)
    }

    // MARK: - 处理单个 item 选中切换（补回）
    func toggleItemSelection(at indexPath: IndexPath, itemIndex: Int) {
        guard isListEditing else { return }
        guard indexPath.section < collections.count, itemIndex < collections[indexPath.section].count else { return }
        if let wid = collections[indexPath.section][itemIndex].worksIdString {
            if selectedIds.contains(wid) {
                selectedIds.remove(wid)
            } else {
                selectedIds.insert(wid)
            }
        }
        if let cell = tableView.cellForRow(at: indexPath) as? ContentCollectionCell {
            cell.updateItemSelectionState(at: itemIndex, isSelected: selectedIds)
        }
        updateAndNotifySelectionState()
    }

    // MARK: - 删除选中项（补回）
    func deleteSelectedItems(completion: @escaping ([[VideoItem]]) -> Void) {
        guard isListEditing else { return }
        var updated: [[VideoItem]] = []
        var didDelete = false
        for section in collections {
            let remain = section.filter { item in
                guard let wid = item.worksIdString else { return true }
                return !selectedIds.contains(wid)
            }
            if remain.count != section.count { didDelete = true }
            updated.append(remain)
        }
        guard didDelete else { return }
        collections = updated
        onDataUpdate?(updated)
        tableView.reloadData()
        updateAndNotifySelectionState()
        completion(updated)
    }

    // MARK: - 计算并通知选中状态（补回）
    private func calculateSelectionState() -> (hasSelection: Bool, allSelected: Bool) {
        let total = collections.reduce(0) { $0 + $1.count }
        let selected = selectedIds.count
        let hasSelection = selected > 0
        let allSelected = total > 0 && selected == total
        return (hasSelection, allSelected)
    }

    func areAllItemsSelected() -> Bool {
        return calculateSelectionState().allSelected
    }

    func updateAndNotifySelectionState() {
        guard isListEditing else { return }
        let state = calculateSelectionState()
        onSelectionChange?(state.hasSelection, state.allSelected)
    }

    // MARK: - 网络加载收藏视频
    private func setupRefresh() {
        // 下拉刷新
        let header = MJRefreshNormalHeader(refreshingTarget: self, refreshingAction: #selector(refreshData))
        // 让刷新提示整体向上偏移，避免被顶部分段控制器遮挡
        header.ignoredScrollViewContentInsetTop = 44 // 与分段控制器高度一致，刷新提示隐藏在其后
        tableView.mj_header = header
        // 上拉加载更多
        tableView.mj_footer = MJRefreshAutoNormalFooter(refreshingTarget: self, refreshingAction: #selector(loadMore))
    }

    @objc private func refreshData() {
        currentPage = 0
        fetchCollectionVideos(isRefresh: true)
    }

    @objc private func loadMore() {
        guard hasMore, !isLoading else {
            tableView.mj_footer?.endRefreshing()
            return
        }
        currentPage += 1
        fetchCollectionVideos(isRefresh: false)
    }

    // MARK: 分页属性
    private var currentPage: Int {
        get { objc_getAssociatedObject(self, &AssociatedKeys.page) as? Int ?? 0 }
        set { objc_setAssociatedObject(self, &AssociatedKeys.page, newValue, .OBJC_ASSOCIATION_RETAIN_NONATOMIC) }
    }
    private var hasMore: Bool {
        get { objc_getAssociatedObject(self, &AssociatedKeys.hasMore) as? Bool ?? true }
        set { objc_setAssociatedObject(self, &AssociatedKeys.hasMore, newValue, .OBJC_ASSOCIATION_RETAIN_NONATOMIC) }
    }
    private var isLoading: Bool {
        get { objc_getAssociatedObject(self, &AssociatedKeys.loading) as? Bool ?? false }
        set { objc_setAssociatedObject(self, &AssociatedKeys.loading, newValue, .OBJC_ASSOCIATION_RETAIN_NONATOMIC) }
    }
    private struct AssociatedKeys { static var page = "cc_page"; static var hasMore = "cc_hasMore"; static var loading = "cc_loading" }

    private func fetchCollectionVideos(isRefresh: Bool) {
        isLoading = true
        APIManager.shared.getUserWorksCollect(page: currentPage, size: 10) { [weak self] result in
            guard let self = self else { return }
            DispatchQueue.main.async {
                self.isLoading = false
                self.tableView.mj_header?.endRefreshing()
                self.tableView.mj_footer?.endRefreshing()
                switch result {
                case .success(let response):
                    guard response.isSuccess, let list = response.data?.list else { return }
                    let items: [VideoItem] = list
                    if isRefresh {
                        // 下拉刷新：替换数据并清空选中状态
                        self.collections = [items]
                        self.selectedIds.removeAll()
                    } else {
                        // 加载更多：追加到现有数据末尾
                        if self.collections.isEmpty { self.collections.append([]) }
                        self.collections[0].append(contentsOf: items)
                    }
                    // update hasMore
                    if let total = response.data?.total {
                        let loadedCount = self.collections.flatMap { $0 }.count
                        self.hasMore = loadedCount < total
                        if self.hasMore {
                            self.tableView.mj_footer?.isHidden = false
                            self.tableView.mj_footer?.endRefreshing()
                        } else {
                            self.tableView.mj_footer?.isHidden = true
                        }
                    } else {
                        self.hasMore = response.data?.hasMore ?? false
                        if self.hasMore {
                            self.tableView.mj_footer?.isHidden = false
                            self.tableView.mj_footer?.endRefreshing()
                        } else {
                            self.tableView.mj_footer?.isHidden = true
                        }
                    }
                    self.tableView.reloadData()
                case .failure(let error):
                    print("收藏视频请求失败: \(error)")
                }
            }
        }
    }

    // MARK: - 对外公开：获取已选中的作品ID，用于网络删除
    func selectedItemIDs() -> [String] {
        return Array(selectedIds)
    }

    // MARK: - 获取用于API调用的纯数字ID列表，返回Int类型
    func selectedNumericItemIDs() -> [Int] {
        var numericIds: [Int] = []
        // 遍历所有选中项
        for section in collections {
            for item in section {
                if let idStr = item.numericIdString, let intVal = Int(idStr), selectedIds.contains(item.worksIdString ?? "") {
                    numericIds.append(intVal)
                }
            }
        }
        return numericIds
    }
}

// MARK: - UITableViewDelegate, UITableViewDataSource
extension ContentCollectionListViewController: UITableViewDelegate, UITableViewDataSource {
    
    func numberOfSections(in tableView: UITableView) -> Int {
        return collections.count
    }
    
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        // 如果分区为空，则不显示该行 (避免空行)
         return collections[section].isEmpty ? 0 : 1
    }
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
         let cell = tableView.dequeueReusableCell(withIdentifier: "ContentCollectionCell", for: indexPath) as! ContentCollectionCell
         let items = collections[indexPath.section]
         // 确保传递正确的 selection handler
         cell.configure(with: items, selectedIds: selectedIds, isEditing: isListEditing) { [weak self] itemIndex in
            guard let self = self else { return }
            if self.isListEditing {
                self.toggleItemSelection(at: indexPath, itemIndex: itemIndex)
            } else {
                self.playVideo(startSection: indexPath.section, itemIndex: itemIndex)
            }
        }
         return cell
     }
    
    func tableView(_ tableView: UITableView, viewForHeaderInSection section: Int) -> UIView? {
         // 如果分区为空，不显示 Header
         guard !collections[section].isEmpty else { return nil }

        let headerView = tableView.dequeueReusableHeaderFooterView(withIdentifier: "CollectionSectionHeaderView") as! CollectionSectionHeaderView
        if section < sectionTitles.count {
            headerView.configure(with: sectionTitles[section])
        }
        return headerView
    }
    
    func tableView(_ tableView: UITableView, heightForHeaderInSection section: Int) -> CGFloat {
         // 如果分区为空，Header 高度为 0
         return collections[section].isEmpty ? 0.1 : 40 // 使用 0.1 避免约束问题
    }
    
    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        // 如果分区为空，行高为0
         guard !collections[indexPath.section].isEmpty else { return 0 }

        let items = collections[indexPath.section]
        let itemWidth = (UIScreen.main.bounds.width - 48) / 3
        let itemHeight = itemWidth * 1.5
        let rows = ceil(Double(items.count) / 3.0)
        let totalHeight = CGFloat(rows) * itemHeight + max(0, CGFloat(rows - 1)) * 8 // 行间距
        return totalHeight
    }
     // 当行消失时，如果外部需要取消选中状态，可以在这里处理
     // func tableView(_ tableView: UITableView, didEndDisplaying cell: UITableViewCell, forRowAt indexPath: IndexPath) { }
}

// MARK: - 分区头视图 - CollectionSectionHeaderView
class CollectionSectionHeaderView: UITableViewHeaderFooterView {
    
    private let titleLabel: UILabel = {
        let label = UILabel()
        label.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        label.textColor = UIColor(hex: "#333333")
        return label
    }()
    
    override init(reuseIdentifier: String?) {
        super.init(reuseIdentifier: reuseIdentifier)
        setupUI()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    private func setupUI() {
        contentView.backgroundColor = UIColor(hex: "#F5F5F5")
        
        contentView.addSubview(titleLabel)
        titleLabel.snp.makeConstraints { make in
            make.left.equalTo(16)
            make.centerY.equalToSuperview()
        }
    }
    
    func configure(with title: String) {
        titleLabel.text = title
    }
}

// MARK: - 内容收藏单元格
class ContentCollectionCell: UITableViewCell {
    
    // 收藏项目集合视图
    private lazy var collectionView: UICollectionView = {
        let layout = UICollectionViewFlowLayout()
        let itemWidth = (UIScreen.main.bounds.width - 48) / 3 // 左右边距各16，间距8
        layout.itemSize = CGSize(width: itemWidth, height: itemWidth * 1.5) // 高度比例1.5
        layout.minimumLineSpacing = 8
        layout.minimumInteritemSpacing = 8
        
        let collectionView = UICollectionView(frame: .zero, collectionViewLayout: layout)
        collectionView.delegate = self
        collectionView.dataSource = self
        collectionView.backgroundColor = .clear
        collectionView.showsVerticalScrollIndicator = false
        collectionView.register(SmallVideoItemCell.self, forCellWithReuseIdentifier: "SmallVideoItemCell")
        collectionView.contentInset = UIEdgeInsets(top: 0, left: 16, bottom: 0, right: 16)
        collectionView.isScrollEnabled = false // 禁用滚动，由外部tableView控制
        
        return collectionView
    }()
    
    // 收藏内容
    private var items: [VideoItem] = []
    private var selectedIds: Set<String> = []
    private var isCellEditing: Bool = false // 当前单元格是否处于编辑模式
    // 闭包，用于通知外部（ContentCollectionListViewController）哪个 item 被点击了
    private var onItemSelect: ((Int) -> Void)?
    
    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        setupUI()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    private func setupUI() {
        backgroundColor = .clear
        selectionStyle = .none
        
        contentView.addSubview(collectionView)
        collectionView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
    }
    
    // 更新 configure 方法以接收编辑状态和闭包
    func configure(with items: [VideoItem], selectedIds: Set<String>, isEditing: Bool, onItemSelect: ((Int) -> Void)?) {
        self.items = items
        self.selectedIds = selectedIds
        self.isCellEditing = isEditing
        self.onItemSelect = onItemSelect
        collectionView.reloadData()
    }

    // 提供一个方法来仅更新特定 Item 的选中状态，避免重载整个 CollectionView
    func updateItemSelectionState(at itemIndex: Int, isSelected selectedIds: Set<String>) {
        guard itemIndex < items.count else { return }
        self.selectedIds = selectedIds
        if let cell = collectionView.cellForItem(at: IndexPath(item: itemIndex, section: 0)) as? SmallVideoItemCell {
             let worksId = items[itemIndex].worksIdString ?? ""
             let isSel = selectedIds.contains(worksId)
             cell.updateSelectionIndicator(isEditing: isCellEditing, isSelected: isSel)
        }
    }

    // MARK: - 新增：刷新当前可见 item 指示器 & 编辑模式切换
    func refreshSelectionIndicators() {
        for indexPath in collectionView.indexPathsForVisibleItems {
            let worksId = items[indexPath.item].worksIdString ?? ""
            let isSelected = selectedIds.contains(worksId)
            if let cell = collectionView.cellForItem(at: indexPath) as? SmallVideoItemCell {
                cell.updateSelectionIndicator(isEditing: isCellEditing, isSelected: isSelected)
            }
        }
    }

    func updateEditing(_ isEditing: Bool, selectedIds: Set<String>) {
        self.isCellEditing = isEditing
        self.selectedIds = selectedIds
        refreshSelectionIndicators()
    }
}

// MARK: - UICollectionViewDelegate, UICollectionViewDataSource
extension ContentCollectionCell: UICollectionViewDelegate, UICollectionViewDataSource {
    
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        return items.count
    }
    
    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        let cell = collectionView.dequeueReusableCell(withReuseIdentifier: "SmallVideoItemCell", for: indexPath) as! SmallVideoItemCell
        
        // 配置单元格
        let item = items[indexPath.item]
        let worksId = item.worksIdString ?? ""
        let isSelected = selectedIds.contains(worksId)
        cell.configure(video: item, isEditing: isCellEditing, isSelected: isSelected)
        
        return cell
    }
    
    func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
        if isCellEditing {
            onItemSelect?(indexPath.item)
        } else {
            onItemSelect?(indexPath.item) // 统一回调，由外部判断编辑态
        }
    }
}

// （移除自定义数据模型，改用 Search 模块已有的 EcommerceProductSearchResponse / EcommerceShopSearchResponse）
// MARK: - 商品收藏列表 (Grid)
class ProductsCollectionListViewController: BaseEditModeListViewController, UICollectionViewDataSource, UICollectionViewDelegate {
    // 数据源
    private var products: [EcommerceProductItem] = []

    // UICollectionView
    private lazy var collectionView: UICollectionView = {
        let layout = UICollectionViewFlowLayout()
        layout.minimumInteritemSpacing = 8
        layout.minimumLineSpacing = 10
        let itemWidth = (UIScreen.main.bounds.width - 12 * 2 - 8) / 2
        layout.itemSize = CGSize(width: itemWidth, height: 270)
        layout.sectionInset = UIEdgeInsets(top: 0, left: 12, bottom: 12, right: 12)

        let cv = UICollectionView(frame: .zero, collectionViewLayout: layout)
        cv.backgroundColor = UIColor(hex: "#F5F5F5")
        cv.delegate = self
        cv.dataSource = self
        cv.register(DiscoverProductCell.self, forCellWithReuseIdentifier: "DiscoverProductCell")
        return cv
    }()

    // 分页 & 状态
    private var currentPage: Int = 0
    private var hasMore: Bool = true
    private var isLoading: Bool = false
    private var selectedIds: Set<Int> = []

    // 空数据占位
    private lazy var emptyView = EmptyPlaceholderView(imageName: "empty_data_placeholder_image", title: "暂未收藏任何商品哦~")

    override func viewDidLoad() {
        super.viewDidLoad()
        view.backgroundColor = UIColor(hex: "#F5F5F5")
        view.addSubview(collectionView)
        collectionView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        view.addSubview(emptyView)
        emptyView.snp.makeConstraints { make in make.edges.equalToSuperview() }
        setupRefresh()
        fetchGoods(isRefresh: true)
    }

    private func setupRefresh() {
        let header = MJRefreshNormalHeader(refreshingTarget: self, refreshingAction: #selector(refreshData))
        collectionView.mj_header = header
        collectionView.mj_footer = MJRefreshAutoNormalFooter(refreshingTarget: self, refreshingAction: #selector(loadMore))
    }

    @objc private func refreshData() {
        currentPage = 0
        fetchGoods(isRefresh: true)
    }

    @objc private func loadMore() {
        guard hasMore, !isLoading else { collectionView.mj_footer?.endRefreshing(); return }
        currentPage += 1
        fetchGoods(isRefresh: false)
    }

    private func fetchGoods(isRefresh: Bool) {
        isLoading = true
        APIManager.shared.getCollectGoods(page: currentPage, size: 10) { [weak self] result in
            guard let self = self else { return }
            DispatchQueue.main.async {
                self.isLoading = false
                self.collectionView.mj_header?.endRefreshing()
                self.collectionView.mj_footer?.endRefreshing()
                switch result {
                case .success(let resp) where resp.status == 1:
                    let list = resp.data.items
                    if isRefresh { self.products = list } else { self.products.append(contentsOf: list) }
                    let total = resp.data.totalCount
                    self.hasMore = self.products.count < total
                    self.collectionView.reloadData()
                    self.emptyView.isHidden = !self.products.isEmpty
                    self.collectionView.mj_footer?.isHidden = !self.hasMore
                case .success(let resp):
                    print("商品收藏列表失败: \(resp.errMsg ?? "")")
                case .failure(let err):
                    print("网络错误: \(err.localizedDescription)")
                }
            }
        }
    }

    // MARK: - 编辑模式 & 选中逻辑
    override func updateUIForEditingMode(_ isEditing: Bool) {
        super.updateUIForEditingMode(isEditing)
        if !isEditing { selectedIds.removeAll() }
        collectionView.reloadData()
    }

    func areAllItemsSelected() -> Bool {
        return !products.isEmpty && selectedIds.count == products.count
    }

    func setSelectAll(_ selectAll: Bool, completion: ((Bool)->Void)? = nil) {
        if selectAll {
            selectedIds = Set(products.compactMap { $0.goodId })
        } else {
            selectedIds.removeAll()
        }
        collectionView.reloadData()
        completion?(selectAll)
    }

    func selectedNumericItemIDs() -> [Int] { Array(selectedIds) }

    func deleteSelectedItems(completion: @escaping ([EcommerceProductItem]) -> Void) {
        guard !selectedIds.isEmpty else { return }
        products.removeAll { item in selectedIds.contains(item.goodId ?? -1) }
        selectedIds.removeAll()
        collectionView.reloadData()
        emptyView.isHidden = !products.isEmpty
        completion(products)
    }

    // Toggle Selection
    private func toggleSelection(at indexPath: IndexPath) {
        guard indexPath.item < products.count else { return }
        let id = products[indexPath.item].goodId ?? -1
        if selectedIds.contains(id) { selectedIds.remove(id) } else { selectedIds.insert(id) }
        if let cell = collectionView.cellForItem(at: indexPath) as? DiscoverProductCell {
            cell.updateSelectionIndicator(isEditing: isListEditing, isSelected: selectedIds.contains(id))
        }
    }
}
// 更新 collectionView delegate
extension ProductsCollectionListViewController {
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        return products.count
    }
    func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
        if isListEditing {
            toggleSelection(at: indexPath)
        } else {
            // TODO: push product detail page
        }
    }
    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        let cell = collectionView.dequeueReusableCell(withReuseIdentifier: "DiscoverProductCell", for: indexPath) as! DiscoverProductCell
        let item = products[indexPath.item]
        cell.configureWithEcommerceItem(item)
        let isSel = selectedIds.contains(item.goodId ?? -1)
        cell.updateSelectionIndicator(isEditing: isListEditing, isSelected: isSel)
        return cell
    }
}

// MARK: - 店铺收藏列表 (List)
class ShopsCollectionListViewController: BaseEditModeListViewController, UITableViewDataSource, UITableViewDelegate {
    // 数据源
    private var shops: [EcommerceShopItem] = []
    private var currentPage: Int = 0
    private var hasMore: Bool = true
    private var isLoading: Bool = false
    private var selectedIds: Set<Int> = []

    private lazy var tableView: UITableView = {
        let tv = UITableView(frame: .zero, style: .plain)
        tv.backgroundColor = UIColor(hex: "#F5F5F5")
        tv.separatorStyle = .none
        tv.dataSource = self
        tv.delegate = self
        tv.register(MerchantTableViewCell.self, forCellReuseIdentifier: MerchantTableViewCell.reuseIdentifier)
        return tv
    }()

    private lazy var emptyView = EmptyPlaceholderView(imageName: "empty_data_placeholder_image", title: "暂未收藏任何店铺哦~")

    override func viewDidLoad() {
        super.viewDidLoad()
        view.backgroundColor = .white
        view.addSubview(tableView)
        tableView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        view.addSubview(emptyView)
        emptyView.snp.makeConstraints { make in make.edges.equalToSuperview() }
        setupRefresh()
        fetchShops(isRefresh: true)
    }

    private func setupRefresh() {
        tableView.mj_header = MJRefreshNormalHeader(refreshingTarget: self, refreshingAction: #selector(refreshData))
        tableView.mj_footer = MJRefreshAutoNormalFooter(refreshingTarget: self, refreshingAction: #selector(loadMore))
    }

    @objc private func refreshData() { currentPage = 0; fetchShops(isRefresh: true) }
    @objc private func loadMore() {
        guard hasMore, !isLoading else { tableView.mj_footer?.endRefreshing(); return }
        currentPage += 1
        fetchShops(isRefresh: false)
    }

    private func fetchShops(isRefresh: Bool) {
        isLoading = true
        APIManager.shared.getCollectGoodStores(page: currentPage, size: 10) { [weak self] result in
            guard let self = self else { return }
            DispatchQueue.main.async {
                self.isLoading = false
                self.tableView.mj_header?.endRefreshing()
                self.tableView.mj_footer?.endRefreshing()
                switch result {
                case .success(let resp) where resp.status == 1:
                    let list = resp.data.data
                    if isRefresh { self.shops = list } else { self.shops.append(contentsOf: list) }
                    let total = resp.data.total
                    self.hasMore = self.shops.count < total
                    self.tableView.reloadData()
                    self.emptyView.isHidden = !self.shops.isEmpty
                    self.tableView.mj_footer?.isHidden = !self.hasMore
                case .success(let resp):
                    print("店铺收藏列表失败: \(resp.errMsg ?? "")")
                case .failure(let err):
                    print("网络错误: \(err.localizedDescription)")
                }
            }
        }
    }

    // MARK: - 编辑模式 & 选中
    override func updateUIForEditingMode(_ isEditing: Bool) {
        super.updateUIForEditingMode(isEditing)
        if !isEditing { selectedIds.removeAll() }
        tableView.reloadData()
    }

    func selectedNumericItemIDs() -> [Int] { Array(selectedIds) }
    func areAllItemsSelected() -> Bool { !shops.isEmpty && selectedIds.count == shops.count }
    func setSelectAll(_ selectAll: Bool, completion: ((Bool)->Void)? = nil) {
        if selectAll { selectedIds = Set(shops.compactMap { Int($0.id) }) } else { selectedIds.removeAll() }
        tableView.reloadData(); completion?(selectAll)
    }
    func deleteSelectedItems(completion: @escaping ([EcommerceShopItem]) -> Void) {
        guard !selectedIds.isEmpty else { return }
        shops.removeAll { item in selectedIds.contains(Int(item.id) ?? -1) }
        selectedIds.removeAll()
        tableView.reloadData()
        emptyView.isHidden = !shops.isEmpty
        completion(shops)
    }

    private func toggleSelection(at indexPath: IndexPath) {
        guard indexPath.row < shops.count else { return }
        let id = Int(shops[indexPath.row].id) ?? -1
        if selectedIds.contains(id) { selectedIds.remove(id) } else { selectedIds.insert(id) }
        if let cell = tableView.cellForRow(at: indexPath) as? MerchantTableViewCell {
            cell.updateSelectionIndicator(isEditing: isListEditing, isSelected: selectedIds.contains(id))
        }
    }
}
extension ShopsCollectionListViewController {
    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        if isListEditing {
            toggleSelection(at: indexPath)
        } else {
            tableView.deselectRow(at: indexPath, animated: true)
            // TODO: push detail
        }
    }
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        guard let cell = tableView.dequeueReusableCell(withIdentifier: MerchantTableViewCell.reuseIdentifier, for: indexPath) as? MerchantTableViewCell else { return UITableViewCell() }
        let item = shops[indexPath.row]
        cell.configure(with: item)
        let isSel = selectedIds.contains(Int(item.id) ?? -1)
        cell.updateSelectionIndicator(isEditing: isListEditing, isSelected: isSel)
        return cell
    }
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int { shops.count }
}

// MARK: - 底部操作视图约束更新
extension MyCollectionViewController {
    private func updateEditActionsViewConstraints() {
        let totalHeight = editActionsContentHeight + WindowUtil.safeAreaBottom
        editActionsView.snp.remakeConstraints { make in
            make.left.right.equalToSuperview()
            make.height.equalTo(totalHeight)
            make.bottom.equalToSuperview() // 贴住屏幕底部，覆盖安全区
        }
    }
}

// MARK: - Network Extensions (仅临时调试用，正式请拆分文件)
extension APIRouter.Center {
    static func deleteWorksCollect(worksIds: [String]) -> APIRequest {
        APIRequest(
            path: "/api/video/center/deleteWorksCollect",
            method: .post,
            parameters: ["ids": worksIds]
        )
    }

    //获取商品收藏列表  /api/goods/goodsFoot page size
    static func getCollectGoods(page: Int, size: Int) -> APIRequest {
        APIRequest(
            path: "/api/goods/goodsFoot",
            method: .get,
            parameters: ["page": page, "size": size]
        )
    }

    //获取店铺收藏列表 /api/goods/goodStoreList page size
    static func getCollectGoodStores(page: Int, size: Int) -> APIRequest {
        APIRequest(
            path: "/api/goods/goodStoreList",
            method: .get,
            parameters: ["page": page, "size": size]
        )
    }

    //删除商品收藏ids  /api/goods/deleteCollectGoods
    static func deleteCollectGoods(ids: [Int]) -> APIRequest {
        APIRequest(
            path: "/api/goods/deleteCollectGoods",
            method: .post,
            parameters: ["ids": ids]
        )
    }

    //删除店铺收藏ids /api/goods/deleteCollectGoodStore
    static func deleteCollectGoodStore(ids: [Int]) -> APIRequest {
        APIRequest(
            path: "/api/goods/deleteCollectGoodStore",
            method: .post,
            parameters: ["ids": ids]
        )
    }
}

extension APIManager {
    func deleteWorksCollect(
        ids: [String],
        completion: @escaping (Result<BaseResponse, APIError>) -> Void
    ) {
        let request = APIRouter.Center.deleteWorksCollect(worksIds: ids)
        APIService.shared.request(request, completion: completion)
    }

    //获取商品收藏列表
    func getCollectGoods(page: Int, size: Int, completion: @escaping (Result<EcommerceProductSearchResponse, APIError>) -> Void) {
        let request = APIRouter.Center.getCollectGoods(page: page, size: size)
        APIService.shared.request(request, completion: completion)
    }

    //获取店铺收藏列表
    func getCollectGoodStores(page: Int, size: Int, completion: @escaping (Result<EcommerceShopSearchResponse, APIError>) -> Void) {
        let request = APIRouter.Center.getCollectGoodStores(page: page, size: size)
        APIService.shared.request(request, completion: completion)
    }

    //删除商品收藏ids
    func deleteCollectGoods(ids: [Int], completion: @escaping (Result<BaseResponse, APIError>) -> Void) {
        let request = APIRouter.Center.deleteCollectGoods(ids: ids)
        APIService.shared.request(request, completion: completion)
    }

    //删除店铺收藏ids
    func deleteCollectGoodStore(ids: [Int], completion: @escaping (Result<BaseResponse, APIError>) -> Void) {
        let request = APIRouter.Center.deleteCollectGoodStore(ids: ids)
        APIService.shared.request(request, completion: completion)
    }
}

//  MVVM 场景下，把网络请求拆分到专属的「服务层」或「Repository 层」是最佳实践，既能保持 APIManager 精简，也方便后期单元测试与 Mock。
// 以下是常用的目录/模块划分建议，可供参考
// Network
// └── Core（APIService、Interceptor、Error 统一封装）
// └── Router（APIRouter 各功能域拆分）
// └── Service（VideoService、UserService…协议+默认实现）
// Models
// └── DTO（网络 Response/Request DTO）
// └── Domain（业务模型，可用 Swift struct 纯数据）
// Repositories
// └── VideoRepository.swift（依赖 Service，负责领域数据组合、缓存、本地化等）
// ViewModels
// └── xxxViewModel.swift（只依赖 Repository，暴露 @Published/@Published var）
// Views
// └── SwiftUI / UIKit 视图
// 🎯 关键点
// APIManager 仅保留通用加密、图片上传等跨域功能，其余按领域拆分 Service/Repository
// 通过协议 + 依赖注入（Initializer、Environment）让 ViewModel 独立于具体实现，易测试
// Service 调用 APIRouter 生成 request → APIService 发起请求 → decode DTO → 回调
// Repository 处理缓存、分页、数据整合，ViewModel 直接订阅 Repository 输出
// 使用 async/await 或 Combine，保持数据流简洁

// 旧版 deleteSelectedItems 已迁移至统一实现，避免重复定义
// @objc private func deleteSelectedItems() { }

// MARK: - 统一删除逻辑实现（替补缺失）
extension MyCollectionViewController {
    /// 根据当前分页类型删除选中项
    @objc private func deleteSelectedItems() {
        print("删除按钮点击")

        // 1️⃣ 获取当前列表控制器
        guard let currentListVC = listContainerView.validListDict[segmentedView.selectedIndex] else { return }

        // 2️⃣ 根据列表类型准备删除动作
        var deletionAction: (() -> Void)?

        switch currentListVC {
        case let contentVC as ContentCollectionListViewController:
            let ids = contentVC.selectedNumericItemIDs()
            guard !ids.isEmpty else { return }
            deletionAction = { [weak self, weak contentVC] in
                APIManager.shared.deleteWorksCollect(ids: ids.map { String($0) }) { [weak self] result in
                    DispatchQueue.main.async {
                        if case .success(let resp) = result, resp.isSuccess {
                            contentVC?.deleteSelectedItems { _ in self?.toggleEditMode() }
                        }
                    }
                }
            }

        case let goodsVC as ProductsCollectionListViewController:
            let ids = goodsVC.selectedNumericItemIDs()
            guard !ids.isEmpty else { return }
            deletionAction = { [weak self, weak goodsVC] in
                APIManager.shared.deleteCollectGoods(ids: ids) { [weak self] result in
                    DispatchQueue.main.async {
                        if case .success(let resp) = result, resp.isSuccess {
                            goodsVC?.deleteSelectedItems { _ in self?.toggleEditMode() }
                        }
                    }
                }
            }

        case let shopVC as ShopsCollectionListViewController:
            let ids = shopVC.selectedNumericItemIDs()
            guard !ids.isEmpty else { return }
            deletionAction = { [weak self, weak shopVC] in
                APIManager.shared.deleteCollectGoodStore(ids: ids) { [weak self] result in
                    DispatchQueue.main.async {
                        if case .success(let resp) = result, resp.isSuccess {
                            shopVC?.deleteSelectedItems { _ in self?.toggleEditMode() }
                        }
                    }
                }
            }

        default:
            return // 其他类型不支持批量删除
        }

        // 若没有可删除的选中项，则直接返回
        guard let confirmedDeletion = deletionAction else { return }

        // 3️⃣ 弹出二次确认弹窗
        let alert = CommonAlertView(
            title: "确认取消",
            message: "确定后将取消收藏，此操作不可撤销",
            leftButtonTitle: "取消",
            rightButtonTitle: "确认"
        )
        alert.onLeftButtonTap = { [weak alert] in alert?.dismiss() }
        alert.onRightButtonTap = { [weak alert] in
            alert?.dismiss()
            confirmedDeletion()
        }
        alert.show()
    }
}

// MARK: - 非编辑状态点击播放
extension ContentCollectionListViewController {
    private func playVideo(startSection: Int, itemIndex: Int) {
        // 平铺所有 items
        let flatList = collections.flatMap { $0 }
        let startFlatIndex = collections[..<startSection].reduce(0) { $0 + $1.count } + itemIndex
        guard startFlatIndex < flatList.count else { return }

        let playerVC = VideoDisplayCenterViewController(
            videoList: flatList,
            startIndex: startFlatIndex,
            hideNavBackButton: false,
            showCustomNavBar: true,
            needsTabBarOffset: false
        )
        playerVC.hidesBottomBarWhenPushed = true
        if let nav = self.navigationController {
            nav.pushViewController(playerVC, animated: true)
        } else if let root = UIApplication.shared.windows.first(where: { $0.isKeyWindow })?.rootViewController {
            root.present(playerVC, animated: true)
        }
    }
}

// MARK: - 通用空占位视图
class EmptyPlaceholderView: UIView {
    private let imageView = UIImageView()
    private let titleLabel = UILabel()
    init(imageName: String, title: String) {
        super.init(frame: .zero)
        imageView.image = UIImage(named: imageName)
        imageView.contentMode = .scaleAspectFit
        titleLabel.text = title
        titleLabel.textColor = UIColor(hex: "#999999")
        titleLabel.font = UIFont.systemFont(ofSize: 14)
        titleLabel.textAlignment = .center
        titleLabel.numberOfLines = 2
        let stack = UIStackView(arrangedSubviews: [imageView, titleLabel])
        stack.axis = .vertical
        stack.alignment = .center
        stack.spacing = 12
        addSubview(stack)
        stack.snp.makeConstraints { make in
            make.center.equalToSuperview()
        }
        imageView.snp.makeConstraints { make in
            make.width.height.equalTo(120)
        }
    }
    required init?(coder: NSCoder) { fatalError("init(coder:) has not been implemented") }
}
