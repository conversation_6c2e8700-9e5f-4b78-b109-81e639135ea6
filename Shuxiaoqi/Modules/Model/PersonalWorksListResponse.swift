//
//  PersonalWorksListResponse.swift
//  Shuxiaoqi
//
//  Created by yongsheng ye on 2025/5/9.
//

import SmartCodable

struct PersonalWorksListResponse: SmartCodable {
    var status: Int = 0
    var errMsg: String = ""
    var msg: String = ""
    var data: PersonalWorksListData?

    var isSuccess: Bool {
        return status == 200
    }

    var displayMessage: String {
        if !msg.isEmpty {
            return msg
        }
        if !errMsg.isEmpty {
            return errMsg
        }
        return isSuccess ? "成功" : "未知错误"
    }
}

struct PersonalWorksListData: SmartCodable {
    var pageNum: Int = 0
    var pageSize: Int = 0
    var total: Int = 0
    var list: [PersonalWorksListDetailData] = []
    var empty: Bool = false
}

struct PersonalWorksListDetailData: SmartCodable {
    var createBy: String? = nil
    var updateBy: String? = nil
    var createTime: String? = nil
    var updateTime: String? = nil
    var id: Int = 0
    var worksType: Int = 0
    var worksUrl: String = ""
    var worksTitle: String = "请添加标题"
    var worksCoverImg: String = ""
    var videoId: String? = nil
    var worksDescribe: String = ""
    var privacy: Int? = nil
    var worksCategoryId: Int = 0
    var worksCategoryName: String? = nil
    var lng: String? = nil
    var lat: String? = nil
    var address: String = ""
    var allowComment: Int = 0
    var followComment: Int = 0
    var extValue: String? = nil
    var duration: Int = 0
    var size: Int = 0
    var likeNumber: Int? = 0
    var watchNumber: Int? = 0
    var commentNumber: Int? = 0
    var shareNumber: Int? = 0
    var collectNumber: Int? = 0
    var state: Int? = nil
    var examineRefuseReason: String? = nil
    var goodIds: String? = nil
    var flowState: Int? = nil
    
    // 调试方法
    func description() -> String {
        let urlString = worksUrl
        // 尝试解析worksUrl字段中的JSON数组
        var urlsDesc = "无法解析"
        if let data = urlString.data(using: .utf8) {
            do {
                if let json = try JSONSerialization.jsonObject(with: data, options: []) as? [Any] {
                    // 过滤掉null值并转换为字符串
                    let validUrls = json.compactMap { $0 as? String }
                    urlsDesc = validUrls.joined(separator: ", ")
                }
            } catch {
                urlsDesc = "解析失败: \(error.localizedDescription)"
            }
        }
        
        return "ID: \(id), 标题: \(worksTitle), 封面: \(worksCoverImg), URLs: \(urlsDesc)"
    }
}
